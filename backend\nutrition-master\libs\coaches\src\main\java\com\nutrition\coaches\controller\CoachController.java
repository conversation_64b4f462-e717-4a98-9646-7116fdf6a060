package com.nutrition.coaches.controller;

import com.nutrition.coaches.entity.Coach;
import com.nutrition.coaches.entity.CoachReview;
import com.nutrition.coaches.service.CoachService;
import com.nutrition.coaches.service.CoachReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/coaches")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CoachController {

    private final CoachService coachService;
    private final CoachReviewService reviewService;

    @Autowired
    public CoachController(CoachService coachService, CoachReviewService reviewService) {
        this.coachService = coachService;
        this.reviewService = reviewService;
    }

    /**
     * Get all coaches with pagination and filtering
     */
    @GetMapping
    public ResponseEntity<Page<Coach>> getCoaches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "rating") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection,
            @RequestParam(required = false) List<String> targetGoals,
            @RequestParam(required = false) List<String> specialties,
            @RequestParam(required = false) List<String> environments,
            @RequestParam(required = false) BigDecimal minRating,
            @RequestParam(required = false) Integer minExperience,
            @RequestParam(required = false) Boolean isPro,
            @RequestParam(required = false) Boolean isFavorite,
            @RequestParam(required = false) String search) {

        try {
            Page<Coach> coaches;

            // Check if any filters are applied
            if (targetGoals != null || specialties != null || environments != null ||
                minRating != null || minExperience != null || isPro != null ||
                isFavorite != null || search != null) {
                
                coaches = coachService.getCoachesWithFilters(
                    targetGoals, specialties, environments,
                    minRating, minExperience, isPro, isFavorite, search,
                    page, size, sortBy, sortDirection
                );
            } else {
                coaches = coachService.getAllCoaches(page, size, sortBy, sortDirection);
            }

            return ResponseEntity.ok(coaches);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get coach by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<Coach> getCoachById(@PathVariable String id) {
        try {
            Optional<Coach> coach = coachService.getCoachByIdWithRelations(id);
            
            if (coach.isPresent()) {
                return ResponseEntity.ok(coach.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Search coaches
     */
    @GetMapping("/search")
    public ResponseEntity<Page<Coach>> searchCoaches(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "rating") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {

        try {
            Page<Coach> coaches = coachService.searchCoaches(query, page, size, sortBy, sortDirection);
            return ResponseEntity.ok(coaches);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get top rated coaches
     */
    @GetMapping("/top-rated")
    public ResponseEntity<Page<Coach>> getTopRatedCoaches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            Page<Coach> coaches = coachService.getTopRatedCoaches(page, size);
            return ResponseEntity.ok(coaches);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get most reviewed coaches
     */
    @GetMapping("/most-reviewed")
    public ResponseEntity<Page<Coach>> getMostReviewedCoaches(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            Page<Coach> coaches = coachService.getMostReviewedCoaches(page, size);
            return ResponseEntity.ok(coaches);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Toggle favorite status for a coach
     */
    @PostMapping("/{id}/favorite")
    public ResponseEntity<Map<String, Object>> toggleFavorite(
            @PathVariable String id,
            @RequestParam(required = false) String userId) {

        try {
            Coach updatedCoach = coachService.toggleFavorite(id, userId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "isFavorite", updatedCoach.getIsFavorite(),
                "message", "Favorite status updated successfully"
            ));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Create new coach
     */
    @PostMapping
    public ResponseEntity<Coach> createCoach(@RequestBody Coach coach) {
        try {
            Coach createdCoach = coachService.createCoach(coach);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCoach);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Update coach
     */
    @PutMapping("/{id}")
    public ResponseEntity<Coach> updateCoach(@PathVariable String id, @RequestBody Coach coach) {
        try {
            Coach updatedCoach = coachService.updateCoach(id, coach);
            return ResponseEntity.ok(updatedCoach);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Delete coach
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteCoach(@PathVariable String id) {
        try {
            coachService.deleteCoach(id);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Coach deleted successfully"
            ));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get coach reviews
     */
    @GetMapping("/{id}/reviews")
    public ResponseEntity<Page<CoachReview>> getCoachReviews(
            @PathVariable String id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            Page<CoachReview> reviews = reviewService.getReviewsByCoachId(id, page, size);
            return ResponseEntity.ok(reviews);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Add review for coach
     */
    @PostMapping("/{id}/reviews")
    public ResponseEntity<CoachReview> addCoachReview(
            @PathVariable String id,
            @RequestBody CoachReview review) {

        try {
            // Set the coach ID
            review.setCoach(coachService.getCoachById(id).orElse(null));
            if (review.getCoach() == null) {
                return ResponseEntity.notFound().build();
            }

            CoachReview createdReview = reviewService.createReview(review);
            
            // Update coach rating after adding review
            coachService.updateCoachRating(id);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdReview);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Update review
     */
    @PutMapping("/{coachId}/reviews/{reviewId}")
    public ResponseEntity<CoachReview> updateCoachReview(
            @PathVariable String coachId,
            @PathVariable String reviewId,
            @RequestBody CoachReview review) {

        try {
            CoachReview updatedReview = reviewService.updateReview(reviewId, review);
            
            // Update coach rating after updating review
            coachService.updateCoachRating(coachId);
            
            return ResponseEntity.ok(updatedReview);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Delete review
     */
    @DeleteMapping("/{coachId}/reviews/{reviewId}")
    public ResponseEntity<Map<String, Object>> deleteCoachReview(
            @PathVariable String coachId,
            @PathVariable String reviewId) {

        try {
            reviewService.deleteReview(reviewId);
            
            // Update coach rating after deleting review
            coachService.updateCoachRating(coachId);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Review deleted successfully"
            ));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get coaches count with filters
     */
    @GetMapping("/count")
    public ResponseEntity<Map<String, Object>> getCoachesCount(
            @RequestParam(required = false) List<String> targetGoals,
            @RequestParam(required = false) List<String> specialties,
            @RequestParam(required = false) List<String> environments,
            @RequestParam(required = false) BigDecimal minRating,
            @RequestParam(required = false) Integer minExperience,
            @RequestParam(required = false) Boolean isPro,
            @RequestParam(required = false) Boolean isFavorite) {

        try {
            Long count = coachService.getCoachesCountWithFilters(
                targetGoals, specialties, environments,
                minRating, minExperience, isPro, isFavorite
            );
            
            return ResponseEntity.ok(Map.of(
                "count", count,
                "success", true
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "coaches-api",
            "timestamp", System.currentTimeMillis()
        ));
    }
} 