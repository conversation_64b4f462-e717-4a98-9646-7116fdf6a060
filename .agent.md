# HealthTech Nutrition Platform - Developer Guide

## Project Overview

**HealthTech** is a comprehensive nutrition and health tracking platform built as a full-stack monorepo using Nx. The application provides nutrition tracking, health coaching, user profiling, and wellness management features across web and mobile platforms.

### Technology Stack

- **Frontend**: Angular 19, Ionic 8, TypeScript, NgRx Signals
- **Backend**: Spring Boot (Java 22), PostgreSQL, Maven
- **Mobile**: Capacitor for iOS/Android deployment
- **Build System**: Nx monorepo with shared libraries
- **Authentication**: JWT, OAuth2 (Google, Facebook, LinkedIn, GitHub)
- **Additional**: Akka for job processing, video/photo processing

## Project Structure

### Applications (`apps/`)

- **`nutrition/`** - Main Ionic Angular mobile/web application
- **`nutrition-admin/`** - Administrative interface

### Frontend Libraries (`libs/`)

- **`auth/`** - Authentication components and services
- **`coaches/`** - Health coach profiles, reviews, and filtering
- **`explorer/`** - Health content exploration (nutrition, workout, mental health)
- **`journal/`** - Daily health tracking (meals, water, exercise, mental activity)
- **`user-profile/`** - User data, preferences, medical info, onboarding
- **`plans/`** - Meal and workout planning
- **`shared/`** - Common UI components, services, and utilities
- **`playground/`** - Development testing components

### Backend Services (`backend/nutrition-master/libs/`)

- **`security/`** - Authentication, OAuth2, JWT management
- **`products/`** - Food product database and nutrition data
- **`coaches/`** - Coach management and reviews
- **`user-profile/`** - User data and preferences
- **`questionnaire/`** - Health assessments and surveys
- **`video/`** - Video content management and processing
- **`photo/`** - Image upload and CDN management
- **`jobs/`** - Background task processing with Akka
- **`nutrition/`** - Core nutrition calculations
- **`shared/`** - Common utilities and configurations

## Development Setup

### Prerequisites

- Node.js (latest LTS)
- Java 22
- PostgreSQL
- Maven

### Quick Start

```bash
# Install dependencies
npm install

# Start frontend development server
npm run start
# or for mobile development
npm run start-for-mobile

# Start backend (from backend/nutrition-master/nutrition-web/)
mvn spring-boot:run

# Build applications
npm run build
npm run "build admin"
```

### Database Configuration

PostgreSQL connection configured in `backend/nutrition-master/nutrition-web/src/main/resources/application.yml`:
- Database: `nutrition`
- Default credentials: `postgres/postgres`
- Port: `5432`

## Angular Development Guidelines

### Component Generation

Use Nx generators for consistent component creation:

```bash
npx nx g @nx/angular:component libs/explorer/src/lib/explorer/component-name/component-name --standalone=true --no-interactive
```

### Component Structure

- **Standalone components** with individual folders
- **Signal-based state management** using NgRx Signals
- **Ionic components** for UI consistency
- **SCSS styling** with component-specific files

### Angular Patterns

#### Signals and Inputs
```typescript
@Component({
  selector: 'user-profile',
  standalone: true,
  template: `{{ email() }}`,
})
export class UserProfile {
  email = input.required<string>();
  changed = output({alias: 'valueChanged'});
}
```

#### Control Flow (Angular 17+)
```html
@for (item of items; track item.id) {
  <li>{{ item.name }}</li>
} @empty {
  <li>No items available</li>
}

@if (condition) {
  <p>Condition is true</p>
} @else {
  <p>Condition is false</p>
}
```

#### State Management
```typescript
export const FeatureStore = signalStore(
  withState(initialState),
  withMethods((store) => ({
    updateData: rxMethod<Data>(
      pipe(
        switchMap((data) => 
          inject(ApiService).updateData(data).pipe(
            tapResponse({
              next: (result) => patchState(store, { data: result }),
              error: console.error,
            })
          )
        )
      )
    ),
  }))
);
```

## Spring Boot Development Guidelines

### Configuration

- **Use `.yml` files** for all configuration
- **Create aggregation configuration classes** for each module:

```java
@EntityScan({"com.nutrition.libs.nutrition"})
@ComponentScan({"com.nutrition.libs.nutrition"})
@EnableJpaRepositories({"com.nutrition.libs.nutrition"})
@Configuration
public class NutritionConfiguration {
}
```

### Architecture Patterns

- **Domain-driven design** with separate libs for each domain
- **Repository pattern** for data access
- **Service layer** for business logic
- **Controller layer** for REST endpoints
- **DTO pattern** for data transfer

### Key Dependencies

- Spring Boot Web, Data JPA, Security
- PostgreSQL driver
- OAuth2 client support
- File upload handling (500MB max)
- Video processing (JAVE library)

## Ionic Development Guidelines

### Project Structure
- **Feature-based organization** in `apps/nutrition/src/app/features/`
- **Shared components** in `apps/nutrition/src/app/shared/`
- **Environment-specific** configurations

### UI Guidelines
- **Prefer Ionic components** for consistency
- **SCSS styling** with theme variables
- **Responsive design** for web and mobile
- **Accessibility considerations**

### Native Features
- **Camera integration** via Capacitor Camera plugin
- **File system access** for photo/video storage
- **Platform-specific** handling with fallbacks

## File Upload System

### Photo Upload
- **Client-side validation**: 10MB max, image types only
- **Server-side processing**: UUID filenames, date-based storage
- **CDN serving**: Static resource configuration with caching
- **Error handling**: Comprehensive validation and user feedback

### Video Processing
- **Upload support**: Large file handling (500MB max)
- **Processing pipeline**: Background job processing with Akka
- **Storage**: Local CDN with configurable paths

## Testing Guidelines

### Frontend Testing
- **Jest** for unit testing
- **Component testing** with Angular Testing Utilities
- **Service testing** with HTTP mocking
- **Store testing** for NgRx Signals

### Backend Testing
- **JUnit** for unit testing
- **Spring Boot Test** for integration testing
- **Test profiles** with separate configurations
- **Mock services** for external dependencies

## Build and Deployment

### Frontend Build
```bash
# Development build
nx build nutrition

# Production build
nx build nutrition --configuration=production

# Mobile build
nx run nutrition:cap-sync-android
```

### Backend Build
```bash
# From backend/nutrition-master/nutrition-web/
mvn clean package
mvn spring-boot:run
```

### Environment Configuration
- **Development**: `environment.ts`
- **Production**: `environment.prod.ts`
- **Backend**: `application.yml` with profiles

## Code Quality and Standards

### Naming Conventions
- **camelCase**: functions, variables
- **kebab-case**: file names
- **PascalCase**: classes, components
- **UPPERCASE**: constants

### Code Organization
- **Feature modules** with clear boundaries
- **Shared utilities** in common libraries
- **Consistent imports** and exports
- **Type safety** with TypeScript

### Performance Considerations
- **Lazy loading** for route modules
- **OnPush change detection** where applicable
- **Efficient state management** with signals
- **Image optimization** and caching

## Security Considerations

### Authentication
- **JWT tokens** with refresh mechanism
- **OAuth2 integration** for social login
- **Role-based access control**
- **Secure token storage**

### File Upload Security
- **File type validation** (client and server)
- **Size limitations** (10MB photos, 500MB videos)
- **UUID filenames** to prevent conflicts
- **Secure static serving** with proper headers

## Documentation and Resources

### Key Documentation Files
- **`docs/PHOTO_UPLOAD_SYSTEM.md`** - Comprehensive photo upload guide
- **`docs/nutrition-design.json`** - Design system specifications
- **`.cursor/rules/`** - Development guidelines and patterns

### Useful Commands
```bash
# Generate new library
nx g @nx/angular:library --directory=feature-name --routing=true

# Run tests
nx test library-name

# Lint code
nx lint library-name

# View project graph
nx graph
```

## Common Patterns and Best Practices

### State Management
- **Use NgRx Signals** for reactive state
- **Centralized stores** for shared data
- **Local component state** for UI-only data

### API Integration
- **Centralized API service** in shared library
- **Error handling** with user-friendly messages
- **Loading states** with proper UX feedback

### Component Design
- **Reusable components** in shared library
- **Feature-specific components** in respective modules
- **Proper input/output** interfaces

### Mobile Development
- **Platform detection** for conditional features
- **Native plugin integration** through services
- **Responsive design** for various screen sizes

## Troubleshooting

### Common Issues
- **CORS errors**: Check proxy configuration in `proxy.conf.js`
- **Build failures**: Verify Node.js and Java versions
- **Database connection**: Ensure PostgreSQL is running
- **Mobile builds**: Check Capacitor configuration

### Development Tools
- **Nx Console** for VS Code integration
- **Angular DevTools** for debugging
- **Spring Boot DevTools** for hot reload
- **Browser DevTools** for mobile debugging