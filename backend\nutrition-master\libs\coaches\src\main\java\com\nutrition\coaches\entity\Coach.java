package com.nutrition.coaches.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "coaches")
public class Coach {

    @Id
    @Column(name = "id", length = 36)
    private String id;

    @NotNull
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 200)
    @Column(name = "title", length = 200)
    private String title;

    @Size(max = 500)
    @Column(name = "avatar", length = 500)
    private String avatar;

    @NotNull
    @DecimalMin("0.0")
    @DecimalMax("10.0")
    @Column(name = "rating", nullable = false, precision = 3, scale = 1)
    private BigDecimal rating = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "review_count", nullable = false)
    private Integer reviewCount = 0;

    @Lob
    @Column(name = "bio", columnDefinition = "TEXT")
    private String bio;

    @Size(max = 500)
    @Column(name = "motto", length = 500)
    private String motto;

    @Min(0)
    @Column(name = "experience")
    private Integer experience;

    @Column(name = "is_pro")
    private Boolean isPro = false;

    @Column(name = "is_favorite")
    private Boolean isFavorite = false;

    @Size(max = 200)
    @Column(name = "location", length = 200)
    private String location;

    @Min(0)
    @Column(name = "session_count")
    private Integer sessionCount = 0;

    @DecimalMin("0.00")
    @Column(name = "price_per_session", precision = 8, scale = 2)
    private BigDecimal pricePerSession;

    @Size(max = 500)
    @Column(name = "video_url", length = 500)
    private String videoUrl;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Many-to-Many relationships
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "coach_target_goals",
        joinColumns = @JoinColumn(name = "coach_id"),
        inverseJoinColumns = @JoinColumn(name = "goal_id")
    )
    private Set<TargetGoal> targetGoals = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "coach_specialties",
        joinColumns = @JoinColumn(name = "coach_id"),
        inverseJoinColumns = @JoinColumn(name = "specialty_id")
    )
    private Set<Specialty> specialties = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "coach_environments",
        joinColumns = @JoinColumn(name = "coach_id"),
        inverseJoinColumns = @JoinColumn(name = "environment_id")
    )
    private Set<Environment> environments = new HashSet<>();

    // One-to-Many relationships
    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<Certification> certifications;

    @OneToMany(mappedBy = "coach", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<CoachReview> reviews;

    // Constructors
    public Coach() {
    }

    public Coach(String id, String name) {
        this.id = id;
        this.name = name;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getMotto() {
        return motto;
    }

    public void setMotto(String motto) {
        this.motto = motto;
    }

    public Integer getExperience() {
        return experience;
    }

    public void setExperience(Integer experience) {
        this.experience = experience;
    }

    public Boolean getIsPro() {
        return isPro;
    }

    public void setIsPro(Boolean isPro) {
        this.isPro = isPro;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSessionCount() {
        return sessionCount;
    }

    public void setSessionCount(Integer sessionCount) {
        this.sessionCount = sessionCount;
    }

    public BigDecimal getPricePerSession() {
        return pricePerSession;
    }

    public void setPricePerSession(BigDecimal pricePerSession) {
        this.pricePerSession = pricePerSession;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public Set<TargetGoal> getTargetGoals() {
        return targetGoals;
    }

    public void setTargetGoals(Set<TargetGoal> targetGoals) {
        this.targetGoals = targetGoals;
    }

    public Set<Specialty> getSpecialties() {
        return specialties;
    }

    public void setSpecialties(Set<Specialty> specialties) {
        this.specialties = specialties;
    }

    public Set<Environment> getEnvironments() {
        return environments;
    }

    public void setEnvironments(Set<Environment> environments) {
        this.environments = environments;
    }

    public List<Certification> getCertifications() {
        return certifications;
    }

    public void setCertifications(List<Certification> certifications) {
        this.certifications = certifications;
    }

    public List<CoachReview> getReviews() {
        return reviews;
    }

    public void setReviews(List<CoachReview> reviews) {
        this.reviews = reviews;
    }

    // Helper methods for managing relationships
    public void addTargetGoal(TargetGoal targetGoal) {
        this.targetGoals.add(targetGoal);
    }

    public void removeTargetGoal(TargetGoal targetGoal) {
        this.targetGoals.remove(targetGoal);
    }

    public void addSpecialty(Specialty specialty) {
        this.specialties.add(specialty);
    }

    public void removeSpecialty(Specialty specialty) {
        this.specialties.remove(specialty);
    }

    public void addEnvironment(Environment environment) {
        this.environments.add(environment);
    }

    public void removeEnvironment(Environment environment) {
        this.environments.remove(environment);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Coach)) return false;
        Coach coach = (Coach) o;
        return getId() != null && getId().equals(coach.getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "Coach{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", title='" + title + '\'' +
                ", rating=" + rating +
                ", reviewCount=" + reviewCount +
                ", experience=" + experience +
                ", location='" + location + '\'' +
                '}';
    }
} 