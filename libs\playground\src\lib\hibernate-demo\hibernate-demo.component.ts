import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { addIcons } from 'ionicons';
import { arrowBackOutline } from 'ionicons/icons';

interface Description {
  hobby: string;
  favoriteColor: string;
  age: number;
  bio: string;
}

interface Person {
  id?: number;
  name: string;
  email: string;
  description: Description;
  skills: string[];
  languages: string[];
  createdAt?: string;
  updatedAt?: string;
}

@Component({
  selector: 'lib-hibernate-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, IonicModule, RouterModule],
  template: `
    <ion-content>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button routerLink="/playground">
              <ion-icon name="arrow-back-outline"></ion-icon>
              Back to Playground
            </ion-button>
          </ion-buttons>
          <ion-title>Hibernate Persistence Utils Demo</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="container">
        <!-- Controls Section -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>Demo Controls</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-segment [(ngModel)]="selectedView" (ionChange)="onViewChange($event)">
              <ion-segment-button value="list">
                <ion-label>View All</ion-label>
              </ion-segment-button>
              <ion-segment-button value="search">
                <ion-label>Search</ion-label>
              </ion-segment-button>
              <ion-segment-button value="create">
                <ion-label>Create</ion-label>
              </ion-segment-button>
              <ion-segment-button value="stats">
                <ion-label>Statistics</ion-label>
              </ion-segment-button>
            </ion-segment>
          </ion-card-content>
        </ion-card>

        <!-- View All Persons -->
        @if (selectedView() === 'list') {
          <ion-card>
            <ion-card-header>
              <ion-card-title>All Persons</ion-card-title>
              <ion-button fill="outline" (click)="loadAllPersons()">
                <ion-icon name="refresh"></ion-icon>
                Refresh
              </ion-button>
            </ion-card-header>
            <ion-card-content>
              @if (loading()) {
                <ion-spinner></ion-spinner>
              } @else {
                @for (person of persons(); track person.id) {
                  <ion-item>
                    <ion-avatar slot="start">
                      <div class="avatar-placeholder">
                        {{ person.name.charAt(0) }}
                      </div>
                    </ion-avatar>
                    <ion-label>
                      <h2>{{ person.name }}</h2>
                      <p>{{ person.email }}</p>
                      <p><strong>Hobby:</strong> {{ person.description.hobby }}</p>
                      <p><strong>Skills:</strong> {{ person.skills.join(', ') }}</p>
                      <p><strong>Languages:</strong> {{ person.languages.join(', ') }}</p>
                    </ion-label>
                    <ion-button fill="clear" slot="end" (click)="deletePerson(person.id!)">
                      <ion-icon name="trash"></ion-icon>
                    </ion-button>
                  </ion-item>
                } @empty {
                  <ion-item>
                    <ion-label>No persons found</ion-label>
                  </ion-item>
                }
              }
            </ion-card-content>
          </ion-card>
        }

        <!-- Search Section -->
        @if (selectedView() === 'search') {
          <ion-card>
            <ion-card-header>
              <ion-card-title>Search & Filter</ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <ion-grid>
                <ion-row>
                  <ion-col size="12" sizeMd="6">
                    <ion-item>
                      <ion-label position="stacked">Search by Hobby</ion-label>
                      <ion-input 
                        [(ngModel)]="searchFilters.hobby" 
                        placeholder="e.g., Programming">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                  <ion-col size="12" sizeMd="6">
                    <ion-item>
                      <ion-label position="stacked">Search by Skill</ion-label>
                      <ion-input 
                        [(ngModel)]="searchFilters.skill" 
                        placeholder="e.g., Java">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="6">
                    <ion-item>
                      <ion-label position="stacked">Min Age</ion-label>
                      <ion-input 
                        type="number" 
                        [(ngModel)]="searchFilters.minAge" 
                        placeholder="25">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                  <ion-col size="6">
                    <ion-item>
                      <ion-label position="stacked">Max Age</ion-label>
                      <ion-input 
                        type="number" 
                        [(ngModel)]="searchFilters.maxAge" 
                        placeholder="35">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12" sizeMd="6">
                    <ion-item>
                      <ion-label position="stacked">Favorite Color</ion-label>
                      <ion-input 
                        [(ngModel)]="searchFilters.favoriteColor" 
                        placeholder="e.g., Blue">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                  <ion-col size="12" sizeMd="6">
                    <ion-item>
                      <ion-label position="stacked">Language</ion-label>
                      <ion-input 
                        [(ngModel)]="searchFilters.language" 
                        placeholder="e.g., English">
                      </ion-input>
                    </ion-item>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-button expand="block" (click)="performSearch()">
                      <ion-icon name="search"></ion-icon>
                      Search
                    </ion-button>
                  </ion-col>
                </ion-row>
              </ion-grid>

              <!-- Search Results -->
              @if (searchResults().length > 0) {
                <ion-list>
                  <ion-list-header>
                    <ion-label>Search Results ({{ searchResults().length }})</ion-label>
                  </ion-list-header>
                  @for (person of searchResults(); track person.id) {
                    <ion-item>
                      <ion-label>
                        <h2>{{ person.name }}</h2>
                        <p>{{ person.email }}</p>
                        <p><strong>Hobby:</strong> {{ person.description.hobby }}</p>
                        <p><strong>Age:</strong> {{ person.description.age }}</p>
                        <p><strong>Color:</strong> {{ person.description.favoriteColor }}</p>
                      </ion-label>
                    </ion-item>
                  }
                </ion-list>
              }
            </ion-card-content>
          </ion-card>
        }

        <!-- Create Person Section -->
        @if (selectedView() === 'create') {
          <ion-card>
            <ion-card-header>
              <ion-card-title>Create New Person</ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <form #personForm="ngForm" (ngSubmit)="createPerson()">
                <ion-item>
                  <ion-label position="stacked">Name *</ion-label>
                  <ion-input 
                    [(ngModel)]="newPerson.name" 
                    name="name" 
                    required 
                    placeholder="Enter full name">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Email *</ion-label>
                  <ion-input 
                    type="email" 
                    [(ngModel)]="newPerson.email" 
                    name="email" 
                    required 
                    placeholder="Enter email address">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Hobby *</ion-label>
                  <ion-input 
                    [(ngModel)]="newPerson.description.hobby" 
                    name="hobby" 
                    required 
                    placeholder="Enter hobby">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Favorite Color *</ion-label>
                  <ion-input 
                    [(ngModel)]="newPerson.description.favoriteColor" 
                    name="favoriteColor" 
                    required 
                    placeholder="Enter favorite color">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Age *</ion-label>
                  <ion-input 
                    type="number" 
                    [(ngModel)]="newPerson.description.age" 
                    name="age" 
                    required 
                    placeholder="Enter age">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Bio</ion-label>
                  <ion-textarea 
                    [(ngModel)]="newPerson.description.bio" 
                    name="bio" 
                    placeholder="Enter bio">
                  </ion-textarea>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Skills (comma-separated)</ion-label>
                  <ion-input 
                    [(ngModel)]="skillsInput" 
                    name="skills" 
                    placeholder="e.g., Java, Spring Boot, Angular">
                  </ion-input>
                </ion-item>

                <ion-item>
                  <ion-label position="stacked">Languages (comma-separated)</ion-label>
                  <ion-input 
                    [(ngModel)]="languagesInput" 
                    name="languages" 
                    placeholder="e.g., English, Spanish, French">
                  </ion-input>
                </ion-item>

                <ion-button 
                  expand="block" 
                  type="submit" 
                  [disabled]="!personForm.valid || saving()">
                  @if (saving()) {
                    <ion-spinner></ion-spinner>
                  }
                  Create Person
                </ion-button>
              </form>
            </ion-card-content>
          </ion-card>
        }

        <!-- Statistics Section -->
        @if (selectedView() === 'stats') {
          <ion-card>
            <ion-card-header>
              <ion-card-title>Statistics</ion-card-title>
              <ion-button fill="outline" (click)="loadStatistics()">
                <ion-icon name="refresh"></ion-icon>
                Refresh
              </ion-button>
            </ion-card-header>
            <ion-card-content>
              <ion-list>
                <ion-item>
                  <ion-label>
                    <h3>Total Persons</h3>
                    <p>{{ totalCount() }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>Unique Skills</h3>
                    <p>{{ uniqueSkills().length }} skills</p>
                  </ion-label>
                </ion-item>
              </ion-list>

              @if (hobbyStats().length > 0) {
                <ion-list>
                  <ion-list-header>
                    <ion-label>Hobby Distribution</ion-label>
                  </ion-list-header>
                  @for (stat of hobbyStats(); track stat.hobby) {
                    <ion-item>
                      <ion-label>
                        <h3>{{ stat.hobby }}</h3>
                        <p>{{ stat.count }} person(s)</p>
                      </ion-label>
                    </ion-item>
                  }
                </ion-list>
              }

              @if (uniqueSkills().length > 0) {
                <ion-list>
                  <ion-list-header>
                    <ion-label>All Skills</ion-label>
                  </ion-list-header>
                  <ion-item>
                    <ion-label>
                      <p>{{ uniqueSkills().join(', ') }}</p>
                    </ion-label>
                  </ion-item>
                </ion-list>
              }
            </ion-card-content>
          </ion-card>
        }

        <!-- Error/Success Messages -->
        @if (message()) {
          <ion-toast 
            [isOpen]="!!message()" 
            [message]="message()" 
            [duration]="3000"
            (didDismiss)="message.set('')">
          </ion-toast>
        }
      </div>
    </ion-content>
  `,
  styles: [`
    .container {
      padding: 1rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .avatar-placeholder {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(45deg, #3880ff, #52cfff);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
    }

    ion-card {
      margin-bottom: 1rem;
    }

    ion-spinner {
      margin-right: 8px;
    }
  `]
})
export class HibernateDemoComponent implements OnInit {
  private http = inject(HttpClient);
  private readonly baseUrl = '/api/hibernate-demo/persons';

  constructor() {
    addIcons({
      arrowBackOutline
    });
  }

  // Signals for reactive state management
  selectedView = signal<string>('list');
  persons = signal<Person[]>([]);
  searchResults = signal<Person[]>([]);
  totalCount = signal<number>(0);
  uniqueSkills = signal<string[]>([]);
  hobbyStats = signal<any[]>([]);
  loading = signal<boolean>(false);
  saving = signal<boolean>(false);
  message = signal<string>('');

  // Form data
  searchFilters = {
    hobby: '',
    skill: '',
    minAge: null as number | null,
    maxAge: null as number | null,
    favoriteColor: '',
    language: ''
  };

  newPerson: Person = {
    name: '',
    email: '',
    description: {
      hobby: '',
      favoriteColor: '',
      age: 0,
      bio: ''
    },
    skills: [],
    languages: []
  };

  skillsInput = '';
  languagesInput = '';

  ngOnInit() {
    this.loadAllPersons();
  }

  onViewChange(event: any) {
    this.selectedView.set(event.detail.value);
    if (this.selectedView() === 'list') {
      this.loadAllPersons();
    } else if (this.selectedView() === 'stats') {
      this.loadStatistics();
    }
  }

  loadAllPersons() {
    this.loading.set(true);
    this.http.get<Person[]>(this.baseUrl).subscribe({
      next: (persons) => {
        this.persons.set(persons);
        this.loading.set(false);
      },
      error: (error) => {
        console.error('Error loading persons:', error);
        this.message.set('Error loading persons');
        this.loading.set(false);
      }
    });
  }

  performSearch() {
    this.loading.set(true);
    const filters = this.searchFilters;

    // Determine which search to perform based on filled filters
    if (filters.hobby) {
      this.searchByHobby(filters.hobby);
    } else if (filters.skill) {
      this.searchBySkill(filters.skill);
    } else if (filters.minAge && filters.maxAge) {
      this.searchByAgeRange(filters.minAge, filters.maxAge);
    } else if (filters.favoriteColor) {
      this.searchByFavoriteColor(filters.favoriteColor);
    } else if (filters.language) {
      this.searchByLanguage(filters.language);
    } else {
      this.message.set('Please fill at least one search criteria');
      this.loading.set(false);
    }
  }

  private searchByHobby(hobby: string) {
    this.http.get<Person[]>(`${this.baseUrl}/by-hobby/${hobby}`).subscribe({
      next: (results) => {
        this.searchResults.set(results);
        this.loading.set(false);
        this.message.set(`Found ${results.length} person(s) with hobby: ${hobby}`);
      },
      error: (error) => {
        console.error('Error searching by hobby:', error);
        this.message.set('Error searching by hobby');
        this.loading.set(false);
      }
    });
  }

  private searchBySkill(skill: string) {
    this.http.get<Person[]>(`${this.baseUrl}/by-skill/${skill}`).subscribe({
      next: (results) => {
        this.searchResults.set(results);
        this.loading.set(false);
        this.message.set(`Found ${results.length} person(s) with skill: ${skill}`);
      },
      error: (error) => {
        console.error('Error searching by skill:', error);
        this.message.set('Error searching by skill');
        this.loading.set(false);
      }
    });
  }

  private searchByAgeRange(minAge: number, maxAge: number) {
    this.http.get<Person[]>(`${this.baseUrl}/by-age-range?minAge=${minAge}&maxAge=${maxAge}`).subscribe({
      next: (results) => {
        this.searchResults.set(results);
        this.loading.set(false);
        this.message.set(`Found ${results.length} person(s) aged ${minAge}-${maxAge}`);
      },
      error: (error) => {
        console.error('Error searching by age range:', error);
        this.message.set('Error searching by age range');
        this.loading.set(false);
      }
    });
  }

  private searchByFavoriteColor(color: string) {
    this.http.get<Person[]>(`${this.baseUrl}/by-favorite-color/${color}`).subscribe({
      next: (results) => {
        this.searchResults.set(results);
        this.loading.set(false);
        this.message.set(`Found ${results.length} person(s) with favorite color: ${color}`);
      },
      error: (error) => {
        console.error('Error searching by favorite color:', error);
        this.message.set('Error searching by favorite color');
        this.loading.set(false);
      }
    });
  }

  private searchByLanguage(language: string) {
    this.http.get<Person[]>(`${this.baseUrl}/by-language/${language}`).subscribe({
      next: (results) => {
        this.searchResults.set(results);
        this.loading.set(false);
        this.message.set(`Found ${results.length} person(s) speaking: ${language}`);
      },
      error: (error) => {
        console.error('Error searching by language:', error);
        this.message.set('Error searching by language');
        this.loading.set(false);
      }
    });
  }

  createPerson() {
    this.saving.set(true);
    
    // Parse skills and languages from comma-separated strings
    this.newPerson.skills = this.skillsInput.split(',').map(s => s.trim()).filter(s => s);
    this.newPerson.languages = this.languagesInput.split(',').map(l => l.trim()).filter(l => l);

    this.http.post<Person>(this.baseUrl, this.newPerson).subscribe({
      next: (person) => {
        this.message.set('Person created successfully!');
        this.resetForm();
        this.saving.set(false);
        // Refresh the list if we're on the list view
        if (this.selectedView() === 'list') {
          this.loadAllPersons();
        }
      },
      error: (error) => {
        console.error('Error creating person:', error);
        this.message.set('Error creating person');
        this.saving.set(false);
      }
    });
  }

  deletePerson(id: number) {
    this.http.delete(`${this.baseUrl}/${id}`).subscribe({
      next: () => {
        this.message.set('Person deleted successfully!');
        this.loadAllPersons();
      },
      error: (error) => {
        console.error('Error deleting person:', error);
        this.message.set('Error deleting person');
      }
    });
  }

  loadStatistics() {
    // Load total count
    this.http.get<number>(`${this.baseUrl}/count`).subscribe({
      next: (count) => this.totalCount.set(count),
      error: (error) => console.error('Error loading count:', error)
    });

    // Load unique skills
    this.http.get<string[]>(`${this.baseUrl}/unique-skills`).subscribe({
      next: (skills) => this.uniqueSkills.set(skills),
      error: (error) => console.error('Error loading skills:', error)
    });

    // Load hobby statistics
    this.http.get<any>(`${this.baseUrl}/statistics/hobbies`).subscribe({
      next: (stats) => {
        const hobbyArray = Object.entries(stats).map(([hobby, count]) => ({
          hobby,
          count
        }));
        this.hobbyStats.set(hobbyArray);
      },
      error: (error) => console.error('Error loading hobby stats:', error)
    });
  }

  private resetForm() {
    this.newPerson = {
      name: '',
      email: '',
      description: {
        hobby: '',
        favoriteColor: '',
        age: 0,
        bio: ''
      },
      skills: [],
      languages: []
    };
    this.skillsInput = '';
    this.languagesInput = '';
  }
} 