package com.nutrition.hibernatedemo.config;

import com.nutrition.hibernatedemo.entity.Person;
import com.nutrition.hibernatedemo.model.Description;
import com.nutrition.hibernatedemo.repository.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Data initializer to create sample persons with JSON and Array data
 */
@Component
public class DataInitializer implements ApplicationRunner {

    @Autowired
    private PersonRepository personRepository;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // Only initialize if the database is empty
        if (personRepository.count() == 0) {
            initializeSampleData();
        }
    }

    private void initializeSampleData() {
        // Create sample persons with diverse data
        
        // Person 1: Software Developer
        Person person1 = new Person();
        person1.setName("<PERSON> Doe");
        person1.setEmail("<EMAIL>");
        person1.setDescription(new Description(
            "Programming", 
            "Blue", 
            28, 
            "Passionate software developer with 5 years of experience in full-stack development. Loves to code and learn new technologies."
        ));
        person1.setSkills(Arrays.asList("Java", "Spring Boot", "Angular", "PostgreSQL", "Docker"));
        person1.setLanguages(Arrays.asList("English", "Spanish", "French"));

        // Person 2: Data Scientist
        Person person2 = new Person();
        person2.setName("Jane Smith");
        person2.setEmail("<EMAIL>");
        person2.setDescription(new Description(
            "Data Analysis", 
            "Green", 
            32, 
            "Experienced data scientist specializing in machine learning and artificial intelligence. PhD in Computer Science."
        ));
        person2.setSkills(Arrays.asList("Python", "Machine Learning", "TensorFlow", "SQL", "R"));
        person2.setLanguages(Arrays.asList("English", "German", "Italian"));

        // Person 3: UI/UX Designer
        Person person3 = new Person();
        person3.setName("Mike Johnson");
        person3.setEmail("<EMAIL>");
        person3.setDescription(new Description(
            "Design", 
            "Red", 
            26, 
            "Creative UI/UX designer with a keen eye for detail and user experience. Specializes in mobile and web applications."
        ));
        person3.setSkills(Arrays.asList("Figma", "Adobe Photoshop", "Sketch", "Prototyping", "User Research"));
        person3.setLanguages(Arrays.asList("English", "Japanese", "Korean"));

        // Person 4: DevOps Engineer
        Person person4 = new Person();
        person4.setName("Sarah Wilson");
        person4.setEmail("<EMAIL>");
        person4.setDescription(new Description(
            "Programming", 
            "Purple", 
            30, 
            "DevOps engineer with expertise in cloud infrastructure and automation. Passionate about building scalable systems."
        ));
        person4.setSkills(Arrays.asList("AWS", "Kubernetes", "Docker", "Terraform", "Jenkins", "Python"));
        person4.setLanguages(Arrays.asList("English", "French"));

        // Person 5: Product Manager
        Person person5 = new Person();
        person5.setName("Robert Brown");
        person5.setEmail("<EMAIL>");
        person5.setDescription(new Description(
            "Management", 
            "Orange", 
            35, 
            "Strategic product manager with 8 years of experience in tech products. Expert in agile methodologies and market analysis."
        ));
        person5.setSkills(Arrays.asList("Product Strategy", "Agile", "Market Research", "Data Analysis", "Stakeholder Management"));
        person5.setLanguages(Arrays.asList("English", "Spanish", "Portuguese"));

        // Person 6: Mobile Developer
        Person person6 = new Person();
        person6.setName("Emily Davis");
        person6.setEmail("<EMAIL>");
        person6.setDescription(new Description(
            "Programming", 
            "Pink", 
            27, 
            "Mobile app developer specializing in cross-platform development. Enjoys creating innovative mobile solutions."
        ));
        person6.setSkills(Arrays.asList("Flutter", "React Native", "iOS", "Android", "Firebase"));
        person6.setLanguages(Arrays.asList("English", "Chinese", "Thai"));

        // Person 7: Database Administrator
        Person person7 = new Person();
        person7.setName("David Martinez");
        person7.setEmail("<EMAIL>");
        person7.setDescription(new Description(
            "Database Management", 
            "Blue", 
            33, 
            "Senior database administrator with extensive experience in database design, optimization, and security."
        ));
        person7.setSkills(Arrays.asList("PostgreSQL", "MySQL", "MongoDB", "Database Design", "Performance Tuning"));
        person7.setLanguages(Arrays.asList("English", "Spanish", "French"));

        // Person 8: Quality Assurance Engineer
        Person person8 = new Person();
        person8.setName("Lisa Anderson");
        person8.setEmail("<EMAIL>");
        person8.setDescription(new Description(
            "Testing", 
            "Yellow", 
            29, 
            "Dedicated QA engineer focused on ensuring software quality through comprehensive testing strategies and automation."
        ));
        person8.setSkills(Arrays.asList("Selenium", "TestNG", "JIRA", "API Testing", "Performance Testing"));
        person8.setLanguages(Arrays.asList("English", "German"));

        // Person 9: Security Specialist
        Person person9 = new Person();
        person9.setName("Alex Taylor");
        person9.setEmail("<EMAIL>");
        person9.setDescription(new Description(
            "Cybersecurity", 
            "Black", 
            31, 
            "Cybersecurity specialist with expertise in threat analysis, vulnerability assessment, and security architecture."
        ));
        person9.setSkills(Arrays.asList("Penetration Testing", "Network Security", "OWASP", "Incident Response", "Risk Assessment"));
        person9.setLanguages(Arrays.asList("English", "Russian"));

        // Person 10: Frontend Developer
        Person person10 = new Person();
        person10.setName("Maria Garcia");
        person10.setEmail("<EMAIL>");
        person10.setDescription(new Description(
            "Programming", 
            "Turquoise", 
            25, 
            "Frontend developer passionate about creating beautiful and responsive user interfaces. Loves modern web technologies."
        ));
        person10.setSkills(Arrays.asList("Angular", "React", "Vue.js", "TypeScript", "SASS", "Webpack"));
        person10.setLanguages(Arrays.asList("English", "Spanish", "Italian"));

        // Save all persons
        List<Person> persons = Arrays.asList(
            person1, person2, person3, person4, person5,
            person6, person7, person8, person9, person10
        );

        personRepository.saveAll(persons);
        
        System.out.println("Sample data initialized: " + persons.size() + " persons created");
    }
} 