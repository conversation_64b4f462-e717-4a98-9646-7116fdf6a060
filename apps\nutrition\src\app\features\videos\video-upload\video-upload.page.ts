import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonText,
  IonProgressBar,
  IonNote,
  IonList
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { cloudUpload, videocam, checkmarkCircle } from 'ionicons/icons';
import { VideoService } from '../video.service';

@Component({
  selector: 'app-video-upload',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    IonItem,
    IonLabel,
    IonInput,
    IonButton,
    IonIcon,
    IonText,
    IonProgressBar,
    IonNote,
    IonList,
    RouterModule
  ],
  template: `
    <ion-header [translucent]="true">
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button defaultHref="/main-tabs/videos"></ion-back-button>
        </ion-buttons>
        <ion-title>Upload Video</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content [fullscreen]="true">
      <div class="upload-container">
        <!-- Upload Form -->
        <ion-card *ngIf="!isUploading() && !uploadSuccess()">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="videocam"></ion-icon>
              Upload New Video
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-list>
              <ion-item>
                <ion-label position="stacked">Video Title *</ion-label>
                <ion-input
                  [(ngModel)]="videoTitle"
                  placeholder="Enter video title"
                  [disabled]="isUploading()"
                ></ion-input>
              </ion-item>

              <ion-item>
                <ion-label position="stacked">Select Video File *</ion-label>
                <input
                  type="file"
                  accept="video/*"
                  (change)="onFileSelected($event)"
                  [disabled]="isUploading()"
                  class="file-input"
                />
              </ion-item>

              <ion-item *ngIf="selectedFile()">
                <ion-label>
                  <h3>{{ selectedFile()?.name }}</h3>
                  <p>{{ formatFileSize(selectedFile()?.size || 0) }}</p>
                </ion-label>
              </ion-item>
            </ion-list>

            <div class="upload-info">
              <ion-note color="medium">
                <p>Supported formats: MP4, AVI, MOV, WMV</p>
                <p>Maximum file size: 500MB</p>
                <p>Your video will be processed for optimal streaming</p>
              </ion-note>
            </div>

            <ion-button
              expand="block"
              (click)="uploadVideo()"
              [disabled]="!canUpload()"
              class="upload-button"
            >
              <ion-icon name="cloud-upload" slot="start"></ion-icon>
              Upload Video
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- Upload Progress -->
        <ion-card *ngIf="isUploading()">
          <ion-card-header>
            <ion-card-title>Uploading Video</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <div class="upload-progress">
              <ion-text>
                <h3>{{ videoTitle }}</h3>
                <p>Uploading {{ selectedFile()?.name }}...</p>
              </ion-text>
              <ion-progress-bar type="indeterminate" color="primary"></ion-progress-bar>
              <ion-note color="medium">
                <p>Please don't close this page while uploading</p>
              </ion-note>
            </div>
          </ion-card-content>
        </ion-card>

        <!-- Upload Success -->
        <ion-card *ngIf="uploadSuccess()" color="success">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="checkmark-circle"></ion-icon>
              Upload Successful!
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-text>
              <p>Your video "{{ videoTitle }}" has been uploaded successfully and is now being processed.</p>
              <p>You'll be able to watch it once processing is complete.</p>
            </ion-text>
            
            <div class="success-actions">
              <ion-button 
                fill="outline" 
                [routerLink]="['/main-tabs/videos']"
                class="action-button"
              >
                Back to Videos
              </ion-button>
              <ion-button 
                fill="solid" 
                [routerLink]="['/main-tabs/videos/player', uploadedVideoId()]"
                class="action-button"
              >
                View Video
              </ion-button>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
    </ion-content>
  `,
  styles: [`
    .upload-container {
      padding: 1rem;
      max-width: 600px;
      margin: 0 auto;
    }

    .file-input {
      width: 100%;
      padding: 0.5rem 0;
      border: none;
      background: transparent;
    }

    .upload-info {
      margin: 1rem 0;
      padding: 1rem;
      background: var(--ion-color-light);
      border-radius: 8px;
    }

    .upload-info ion-note p {
      margin: 0.25rem 0;
    }

    .upload-button {
      margin-top: 1rem;
    }

    .upload-progress {
      text-align: center;
    }

    .upload-progress ion-progress-bar {
      margin: 1rem 0;
    }

    .success-actions {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
      flex-wrap: wrap;
    }

    .action-button {
      flex: 1;
      min-width: 120px;
    }

    ion-card-title ion-icon {
      margin-right: 0.5rem;
      vertical-align: middle;
    }

    @media (max-width: 768px) {
      .success-actions {
        flex-direction: column;
      }
      
      .action-button {
        width: 100%;
      }
    }
  `]
})
export class VideoUploadPage {
  private readonly videoService = inject(VideoService);
  private readonly router = inject(Router);

  // Signals for reactive state management
  selectedFile = signal<File | null>(null);
  isUploading = signal(false);
  uploadSuccess = signal(false);
  uploadedVideoId = signal<string>('');

  // Form data
  videoTitle = '';

  constructor() {
    addIcons({ cloudUpload, videocam, checkmarkCircle });
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('video/')) {
        alert('Please select a valid video file');
        return;
      }

      // Validate file size (500MB limit)
      const maxSize = 500 * 1024 * 1024; // 500MB in bytes
      if (file.size > maxSize) {
        alert('File size must be less than 500MB');
        return;
      }

      this.selectedFile.set(file);
    }
  }

  canUpload(): boolean {
    return !!(this.videoTitle.trim() && this.selectedFile() && !this.isUploading());
  }

  uploadVideo() {
    if (!this.canUpload()) return;

    const file = this.selectedFile()!;
    const title = this.videoTitle.trim();

    this.isUploading.set(true);

    this.videoService.uploadVideo(file, title).subscribe({
      next: (response) => {
        console.log('Upload successful:', response);
        this.uploadedVideoId.set(response.id);
        this.isUploading.set(false);
        this.uploadSuccess.set(true);
      },
      error: (error) => {
        console.error('Upload failed:', error);
        this.isUploading.set(false);
        alert('Upload failed. Please try again.');
      }
    });
  }

  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}
