package com.nutrition.coaches.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "target_goals")
public class TargetGoal {

    @Id
    @Column(name = "id", length = 36)
    private String id;

    @NotNull
    @Size(max = 100)
    @Column(name = "name", nullable = false, unique = true, length = 100)
    private String name;

    @ManyToMany(mappedBy = "targetGoals", fetch = FetchType.LAZY)
    private Set<Coach> coaches = new HashSet<>();

    // Constructors
    public TargetGoal() {
    }

    public TargetGoal(String id, String name) {
        this.id = id;
        this.name = name;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<Coach> getCoaches() {
        return coaches;
    }

    public void setCoaches(Set<Coach> coaches) {
        this.coaches = coaches;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TargetGoal)) return false;
        TargetGoal that = (TargetGoal) o;
        return getId() != null && getId().equals(that.getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "TargetGoal{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
} 