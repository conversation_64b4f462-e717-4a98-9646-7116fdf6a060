package com.nutrition.nutrition_web;

import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RestController
@RequestMapping("/api/playground")
@CrossOrigin(origins = "*")
public class PlaygroundController {

    private static final List<PlaygroundItem> MOCK_DATA = generateMockData();

    @GetMapping("/items")
    public PlaygroundResponse getItems(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            @RequestParam(defaultValue = "") String filter,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection) {

        // Simulate network delay
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<PlaygroundItem> filteredItems = MOCK_DATA.stream()
                .filter(item -> filter.isEmpty() ||
                        item.getName().toLowerCase().contains(filter.toLowerCase()) ||
                        item.getCategory().toLowerCase().contains(filter.toLowerCase()) ||
                        item.getDescription().toLowerCase().contains(filter.toLowerCase()))
                .collect(Collectors.toList());

        // Sort items
        Comparator<PlaygroundItem> comparator = getComparator(sortBy);
        if ("desc".equalsIgnoreCase(sortDirection)) {
            comparator = comparator.reversed();
        }
        filteredItems.sort(comparator);

        // Paginate
        int start = page * size;
        int end = Math.min(start + size, filteredItems.size());
        List<PlaygroundItem> pageItems = start < filteredItems.size() ?
                filteredItems.subList(start, end) : Collections.emptyList();

        return new PlaygroundResponse(
                pageItems,
                filteredItems.size(),
                page,
                size,
                (int) Math.ceil((double) filteredItems.size() / size),
                page < Math.ceil((double) filteredItems.size() / size) - 1
        );
    }

    private Comparator<PlaygroundItem> getComparator(String sortBy) {
        switch (sortBy.toLowerCase()) {
            case "name":
                return Comparator.comparing(PlaygroundItem::getName);
            case "category":
                return Comparator.comparing(PlaygroundItem::getCategory);
            case "priority":
                return Comparator.comparing(PlaygroundItem::getPriority);
            case "date":
                return Comparator.comparing(PlaygroundItem::getCreatedDate);
            default:
                return Comparator.comparing(PlaygroundItem::getName);
        }
    }

    private static List<PlaygroundItem> generateMockData() {
        String[] categories = {"Technology", "Health", "Education", "Entertainment", "Sports", "Travel", "Food", "Art", "Music", "Science"};
        String[] priorities = {"Low", "Medium", "High", "Critical"};
        String[] adjectives = {"Amazing", "Fantastic", "Incredible", "Outstanding", "Remarkable", "Excellent", "Brilliant", "Exceptional", "Wonderful", "Magnificent"};
        String[] nouns = {"Project", "Task", "Item", "Goal", "Objective", "Assignment", "Activity", "Challenge", "Mission", "Quest"};

        Random random = new Random(42); // Fixed seed for consistent data

        return IntStream.range(1, 1001)
                .mapToObj(i -> {
                    String category = categories[random.nextInt(categories.length)];
                    String adjective = adjectives[random.nextInt(adjectives.length)];
                    String noun = nouns[random.nextInt(nouns.length)];
                    String priority = priorities[random.nextInt(priorities.length)];

                    Date createdDate = new Date(System.currentTimeMillis() - random.nextInt(365 * 24 * 60 * 60 * 1000));

                    return new PlaygroundItem(
                            i,
                            adjective + " " + noun + " #" + i,
                            "This is a detailed description for " + adjective.toLowerCase() + " " + noun.toLowerCase() +
                            " number " + i + ". It belongs to the " + category + " category and demonstrates various features.",
                            category,
                            priority,
                            createdDate,
                            random.nextBoolean()
                    );
                })
                .collect(Collectors.toList());
    }

    public static class PlaygroundItem {
        private int id;
        private String name;
        private String description;
        private String category;
        private String priority;
        private Date createdDate;
        private boolean isActive;

        public PlaygroundItem(int id, String name, String description, String category, String priority, Date createdDate, boolean isActive) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.category = category;
            this.priority = priority;
            this.createdDate = createdDate;
            this.isActive = isActive;
        }

        // Getters and setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }

        public Date getCreatedDate() { return createdDate; }
        public void setCreatedDate(Date createdDate) { this.createdDate = createdDate; }

        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
    }

    public static class PlaygroundResponse {
        private List<PlaygroundItem> items;
        private int totalElements;
        private int currentPage;
        private int pageSize;
        private int totalPages;
        private boolean hasNext;

        public PlaygroundResponse(List<PlaygroundItem> items, int totalElements, int currentPage, int pageSize, int totalPages, boolean hasNext) {
            this.items = items;
            this.totalElements = totalElements;
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.totalPages = totalPages;
            this.hasNext = hasNext;
        }

        // Getters and setters
        public List<PlaygroundItem> getItems() { return items; }
        public void setItems(List<PlaygroundItem> items) { this.items = items; }

        public int getTotalElements() { return totalElements; }
        public void setTotalElements(int totalElements) { this.totalElements = totalElements; }

        public int getCurrentPage() { return currentPage; }
        public void setCurrentPage(int currentPage) { this.currentPage = currentPage; }

        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }

        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

        public boolean isHasNext() { return hasNext; }
        public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }
    }
}
