package com.nutrition.coaches.service;

import com.nutrition.coaches.entity.Coach;
import com.nutrition.coaches.repository.CoachRepository;
import com.nutrition.coaches.repository.CoachReviewRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CoachService {

    private final CoachRepository coachRepository;
    private final CoachReviewRepository reviewRepository;

    @Autowired
    public CoachService(CoachRepository coachRepository, CoachReviewRepository reviewRepository) {
        this.coachRepository = coachRepository;
        this.reviewRepository = reviewRepository;
    }

    /**
     * Get all coaches with pagination
     */
    @Transactional(readOnly = true)
    public Page<Coach> getAllCoaches(int page, int size, String sortBy, String sortDirection) {
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return coachRepository.findAll(pageable);
    }

    /**
     * Get coaches with complex filtering
     */
    @Transactional(readOnly = true)
    public Page<Coach> getCoachesWithFilters(
            List<String> targetGoalIds,
            List<String> specialtyIds,
            List<String> environmentIds,
            BigDecimal minRating,
            Integer minExperience,
            Boolean isPro,
            Boolean isFavorite,
            String searchTerm,
            int page,
            int size,
            String sortBy,
            String sortDirection) {

        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));

        return coachRepository.findWithFilters(
            targetGoalIds, 
            specialtyIds, 
            environmentIds, 
            minRating, 
            minExperience, 
            isPro, 
            isFavorite, 
            searchTerm, 
            pageable
        );
    }

    /**
     * Get coach by ID with all relations
     */
    @Transactional(readOnly = true)
    public Optional<Coach> getCoachByIdWithRelations(String id) {
        return coachRepository.findByIdWithAllRelations(id);
    }

    /**
     * Get coach by ID (simple)
     */
    @Transactional(readOnly = true)
    public Optional<Coach> getCoachById(String id) {
        return coachRepository.findById(id);
    }

    /**
     * Search coaches by name, title, or bio
     */
    @Transactional(readOnly = true)
    public Page<Coach> searchCoaches(String searchTerm, int page, int size, String sortBy, String sortDirection) {
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return coachRepository.searchByNameTitleOrBio(searchTerm, pageable);
    }

    /**
     * Get coaches by target goals
     */
    @Transactional(readOnly = true)
    public Page<Coach> getCoachesByTargetGoals(List<String> goalIds, int page, int size, String sortBy, String sortDirection) {
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return coachRepository.findByTargetGoalIds(goalIds, pageable);
    }

    /**
     * Get coaches by specialties
     */
    @Transactional(readOnly = true)
    public Page<Coach> getCoachesBySpecialties(List<String> specialtyIds, int page, int size, String sortBy, String sortDirection) {
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return coachRepository.findBySpecialtyIds(specialtyIds, pageable);
    }

    /**
     * Get coaches by environments
     */
    @Transactional(readOnly = true)
    public Page<Coach> getCoachesByEnvironments(List<String> environmentIds, int page, int size, String sortBy, String sortDirection) {
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        return coachRepository.findByEnvironmentIds(environmentIds, pageable);
    }

    /**
     * Get top rated coaches
     */
    @Transactional(readOnly = true)
    public Page<Coach> getTopRatedCoaches(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return coachRepository.findTopRatedCoaches(pageable);
    }

    /**
     * Get most reviewed coaches
     */
    @Transactional(readOnly = true)
    public Page<Coach> getMostReviewedCoaches(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return coachRepository.findMostReviewedCoaches(pageable);
    }

    /**
     * Toggle favorite status for a coach
     */
    public Coach toggleFavorite(String coachId, String userId) {
        Optional<Coach> coachOpt = coachRepository.findById(coachId);
        if (coachOpt.isPresent()) {
            Coach coach = coachOpt.get();
            // In a real implementation, this would be user-specific
            // For now, we'll just toggle the global favorite status
            coach.setIsFavorite(!coach.getIsFavorite());
            return coachRepository.save(coach);
        }
        throw new RuntimeException("Coach not found with id: " + coachId);
    }

    /**
     * Create new coach
     */
    public Coach createCoach(Coach coach) {
        // Set initial values
        if (coach.getRating() == null) {
            coach.setRating(BigDecimal.ZERO);
        }
        if (coach.getReviewCount() == null) {
            coach.setReviewCount(0);
        }
        if (coach.getSessionCount() == null) {
            coach.setSessionCount(0);
        }
        if (coach.getIsPro() == null) {
            coach.setIsPro(false);
        }
        if (coach.getIsFavorite() == null) {
            coach.setIsFavorite(false);
        }
        
        return coachRepository.save(coach);
    }

    /**
     * Update existing coach
     */
    public Coach updateCoach(String id, Coach updatedCoach) {
        Optional<Coach> existingCoachOpt = coachRepository.findById(id);
        if (existingCoachOpt.isPresent()) {
            Coach existingCoach = existingCoachOpt.get();
            
            // Update fields
            if (updatedCoach.getName() != null) {
                existingCoach.setName(updatedCoach.getName());
            }
            if (updatedCoach.getTitle() != null) {
                existingCoach.setTitle(updatedCoach.getTitle());
            }
            if (updatedCoach.getAvatar() != null) {
                existingCoach.setAvatar(updatedCoach.getAvatar());
            }
            if (updatedCoach.getBio() != null) {
                existingCoach.setBio(updatedCoach.getBio());
            }
            if (updatedCoach.getMotto() != null) {
                existingCoach.setMotto(updatedCoach.getMotto());
            }
            if (updatedCoach.getExperience() != null) {
                existingCoach.setExperience(updatedCoach.getExperience());
            }
            if (updatedCoach.getLocation() != null) {
                existingCoach.setLocation(updatedCoach.getLocation());
            }
            if (updatedCoach.getPricePerSession() != null) {
                existingCoach.setPricePerSession(updatedCoach.getPricePerSession());
            }
            if (updatedCoach.getVideoUrl() != null) {
                existingCoach.setVideoUrl(updatedCoach.getVideoUrl());
            }
            if (updatedCoach.getIsPro() != null) {
                existingCoach.setIsPro(updatedCoach.getIsPro());
            }

            return coachRepository.save(existingCoach);
        }
        throw new RuntimeException("Coach not found with id: " + id);
    }

    /**
     * Delete coach
     */
    public void deleteCoach(String id) {
        if (coachRepository.existsById(id)) {
            coachRepository.deleteById(id);
        } else {
            throw new RuntimeException("Coach not found with id: " + id);
        }
    }

    /**
     * Get coaches count with filters
     */
    @Transactional(readOnly = true)
    public Long getCoachesCountWithFilters(
            List<String> targetGoalIds,
            List<String> specialtyIds,
            List<String> environmentIds,
            BigDecimal minRating,
            Integer minExperience,
            Boolean isPro,
            Boolean isFavorite) {

        return coachRepository.countWithFilters(
            targetGoalIds, 
            specialtyIds, 
            environmentIds, 
            minRating, 
            minExperience, 
            isPro, 
            isFavorite
        );
    }

    /**
     * Update coach rating based on reviews
     */
    public void updateCoachRating(String coachId) {
        Double averageRating = reviewRepository.getAverageRatingByCoachId(coachId);
        Long reviewCount = reviewRepository.countByCoachId(coachId);
        
        Optional<Coach> coachOpt = coachRepository.findById(coachId);
        if (coachOpt.isPresent()) {
            Coach coach = coachOpt.get();
            coach.setRating(averageRating != null ? BigDecimal.valueOf(averageRating) : BigDecimal.ZERO);
            coach.setReviewCount(reviewCount.intValue());
            coachRepository.save(coach);
        }
    }

    /**
     * Check if coach exists
     */
    @Transactional(readOnly = true)
    public boolean coachExists(String id) {
        return coachRepository.existsById(id);
    }
} 