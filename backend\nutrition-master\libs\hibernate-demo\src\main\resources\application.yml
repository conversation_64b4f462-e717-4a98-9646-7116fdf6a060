# Main Configuration for Hibernate Demo
spring:
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME:nutrition}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
  
  sql:
    init:
      mode: always
      continue-on-error: true

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info

# Logging
logging:
  level:
    com.nutrition.hibernatedemo: INFO
    org.hibernate.SQL: DEBUG 