package com.nutrition.hibernatedemo.controller;

import com.nutrition.hibernatedemo.entity.Person;
import com.nutrition.hibernatedemo.repository.PersonRepository;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST Controller for Person entity demonstrating Hibernate Types functionality
 */
@RestController
@RequestMapping("/api/hibernate-demo/persons")
@CrossOrigin(origins = "*")
public class PersonController {

    @Autowired
    private PersonRepository personRepository;

    // CRUD Operations

    @GetMapping
    public ResponseEntity<List<Person>> getAllPersons() {
        List<Person> persons = personRepository.findAll();
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Person> getPersonById(@PathVariable Long id) {
        Optional<Person> person = personRepository.findById(id);
        return person.map(ResponseEntity::ok)
                    .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<Person> createPerson(@Valid @RequestBody Person person) {
        try {
            Person savedPerson = personRepository.save(person);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedPerson);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Person> updatePerson(@PathVariable Long id, @Valid @RequestBody Person personDetails) {
        Optional<Person> optionalPerson = personRepository.findById(id);
        if (optionalPerson.isPresent()) {
            Person person = optionalPerson.get();
            person.setName(personDetails.getName());
            person.setEmail(personDetails.getEmail());
            person.setDescription(personDetails.getDescription());
            person.setSkills(personDetails.getSkills());
            person.setLanguages(personDetails.getLanguages());
            
            Person updatedPerson = personRepository.save(person);
            return ResponseEntity.ok(updatedPerson);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePerson(@PathVariable Long id) {
        if (personRepository.existsById(id)) {
            personRepository.deleteById(id);
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    // JSON-based filtering endpoints

    @GetMapping("/by-hobby/{hobby}")
    public ResponseEntity<List<Person>> getPersonsByHobby(@PathVariable String hobby) {
        List<Person> persons = personRepository.findByHobby(hobby);
        return ResponseEntity.ok(persons);
    }

    @PostMapping("/by-hobbies")
    public ResponseEntity<List<Person>> getPersonsByHobbies(@RequestBody List<String> hobbies) {
        List<Person> persons = personRepository.findByHobbyIn(hobbies);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/by-age-range")
    public ResponseEntity<List<Person>> getPersonsByAgeRange(
            @RequestParam int minAge, 
            @RequestParam int maxAge) {
        List<Person> persons = personRepository.findByAgeRange(minAge, maxAge);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/by-favorite-color/{color}")
    public ResponseEntity<List<Person>> getPersonsByFavoriteColor(@PathVariable String color) {
        List<Person> persons = personRepository.findByFavoriteColor(color);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/by-bio-keyword/{keyword}")
    public ResponseEntity<List<Person>> getPersonsByBioKeyword(@PathVariable String keyword) {
        List<Person> persons = personRepository.findByBioContaining(keyword);
        return ResponseEntity.ok(persons);
    }

    @PostMapping("/by-json-contains")
    public ResponseEntity<List<Person>> getPersonsByJsonContains(@RequestBody String jsonData) {
        List<Person> persons = personRepository.findByJsonContains(jsonData);
        return ResponseEntity.ok(persons);
    }

    // Array-based filtering endpoints

    @GetMapping("/by-skill/{skill}")
    public ResponseEntity<List<Person>> getPersonsBySkill(@PathVariable String skill) {
        List<Person> persons = personRepository.findBySkill(skill);
        return ResponseEntity.ok(persons);
    }

    @PostMapping("/by-any-skill")
    public ResponseEntity<List<Person>> getPersonsByAnySkill(@RequestBody String[] skills) {
        List<Person> persons = personRepository.findByAnySkill(skills);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/by-language/{language}")
    public ResponseEntity<List<Person>> getPersonsByLanguage(@PathVariable String language) {
        List<Person> persons = personRepository.findByLanguage(language);
        return ResponseEntity.ok(persons);
    }

    @PostMapping("/by-all-languages")
    public ResponseEntity<List<Person>> getPersonsByAllLanguages(@RequestBody String[] languages) {
        List<Person> persons = personRepository.findByAllLanguages(languages);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/by-minimum-skills/{minSkills}")
    public ResponseEntity<List<Person>> getPersonsByMinimumSkills(@PathVariable int minSkills) {
        List<Person> persons = personRepository.findByMinimumSkillCount(minSkills);
        return ResponseEntity.ok(persons);
    }

    // Complex queries

    @GetMapping("/by-hobby-and-skill")
    public ResponseEntity<List<Person>> getPersonsByHobbyAndSkill(
            @RequestParam String hobby, 
            @RequestParam String skill) {
        List<Person> persons = personRepository.findByHobbyAndSkill(hobby, skill);
        return ResponseEntity.ok(persons);
    }

    @GetMapping("/same-skills-as/{personId}")
    public ResponseEntity<List<Person>> getPeopleWithSameSkillsAs(@PathVariable Long personId) {
        List<Person> persons = personRepository.findPeopleWithSameSkillsAs(personId);
        return ResponseEntity.ok(persons);
    }

    // Statistics and analytics endpoints

    @GetMapping("/statistics/hobbies")
    public ResponseEntity<Map<String, Object>> getHobbyStatistics() {
        List<Object[]> stats = personRepository.getHobbyStatistics();
        Map<String, Object> result = new HashMap<>();
        for (Object[] stat : stats) {
            result.put((String) stat[0], stat[1]);
        }
        return ResponseEntity.ok(result);
    }

    @GetMapping("/unique-skills")
    public ResponseEntity<List<String>> getAllUniqueSkills() {
        List<String> skills = personRepository.getAllUniqueSkills();
        return ResponseEntity.ok(skills);
    }

    // Utility endpoints

    @GetMapping("/by-email/{email}")
    public ResponseEntity<Person> getPersonByEmail(@PathVariable String email) {
        Person person = personRepository.findByEmail(email);
        return person != null ? ResponseEntity.ok(person) : ResponseEntity.notFound().build();
    }

    @GetMapping("/count")
    public ResponseEntity<Long> getPersonCount() {
        long count = personRepository.count();
        return ResponseEntity.ok(count);
    }
} 