package com.nutrition.runners;

import com.nutrition.libs.eatntrack.crawler.model.Product;
import com.nutrition.libs.eatntrack.crawler.processor.EatNTrackCrawlingProcess;
import com.nutrition.libs.selenium.WebDriverManagerProvider;
import com.nutrition.libs.shared.util.xls.XlsDataWriter;
import org.openqa.selenium.WebDriver;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.boot.WebApplicationType;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@SpringBootApplication(scanBasePackages = {"com.nutrition.runners"})
@EntityScan({"com.nutrition.runners"})
//@Profile("never")
public class EatnTrackImport implements CommandLineRunner {

    private final EatNTrackCrawlingProcess eatNTrackCrawlingProcess;
    private final WebDriverManagerProvider webDriverManagerProvider;

  public EatnTrackImport(EatNTrackCrawlingProcess eatNTrackCrawlingProcess, WebDriverManagerProvider webDriverManagerProvider) {
        this.eatNTrackCrawlingProcess = eatNTrackCrawlingProcess;
        this.webDriverManagerProvider = webDriverManagerProvider;
    }

    public static void main(String[] args) throws Exception {
        SpringApplication app = new SpringApplication(EatnTrackImport.class);
        app.setWebApplicationType(WebApplicationType.NONE);
       /*  app.setAdditionalProfiles("disable-command-line-runners");
        ConfigurableApplicationContext context = app.run(args);

        EatnTrackImport eatnTrackImport = context.getBean(EatnTrackImport.class); */
        app.run(args);
        //context.close();
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("EatnTrackImport started");
        WebDriver driver = this.webDriverManagerProvider.createDriver();

        // Define the Excel file path
        String filename = "backend/nutrition-master/nutrition-web/src/main/resources/eatntrack_products.xlsx";

        // Get existing product names ONCE before starting the loop
        Set<String> existingProductNames = getExistingProductNames(filename);
        System.out.println("Found " + existingProductNames.size() + " existing products in Excel file");

        int totalProductsAdded = 0;
        int totalProductsSkipped = 0;

        // Process pages one by one using the existing crawl method
        for (int pageNumber = 51; pageNumber <= 111; pageNumber++) {
          try {
            System.out.println("=== Processing page " + pageNumber + " ===");

            // Crawl a single page using the existing crawl method

            List<Product> pageProducts = eatNTrackCrawlingProcess.crawl(driver, pageNumber, pageNumber);

            if (pageProducts == null || pageProducts.isEmpty()) {
              System.out.println("No products found on page " + pageNumber + ", stopping crawl");
              break;
            }

            System.out.println("Found " + pageProducts.size() + " products on page " + pageNumber);

            // Filter out duplicates
            List<Product> newProducts = filterDuplicateProducts(pageProducts, existingProductNames);

            if (newProducts.isEmpty()) {
              System.out.println("All products on page " + pageNumber + " already exist, skipping to next page");
              totalProductsSkipped += pageProducts.size();
              continue;
            }

            System.out.println("Adding " + newProducts.size() + " new products to Excel file");

            // Export to Excel (append mode after first page)
            exportProductsToExcel(newProducts, true);

            // Add new product names to existing set to avoid duplicates in next iterations
            for (Product product : newProducts) {
              if (product.name() != null && !product.name().trim().isEmpty()) {
                existingProductNames.add(product.name().trim());
              }
            }

            totalProductsAdded += newProducts.size();
            totalProductsSkipped += (pageProducts.size() - newProducts.size());

            System.out.println("Page " + pageNumber + " completed. Added: " + newProducts.size() +
              ", Skipped: " + (pageProducts.size() - newProducts.size()));

            // Small delay to be respectful to the server
            try {
              Thread.sleep(1000);
            } catch (InterruptedException e) {
              Thread.currentThread().interrupt();
              break;
            }
          }catch (Exception e) {
            pageNumber--;
            e.printStackTrace();
          }
        }
        driver.close();

        System.out.println("=== EatnTrackImport completed ===");
        System.out.println("Total products added: " + totalProductsAdded);
        System.out.println("Total products skipped (duplicates): " + totalProductsSkipped);
        System.out.println("Excel file: " + filename);


    }

    /**
     * Get existing product names from the Excel file to avoid duplicates
     */
    private Set<String> getExistingProductNames(String filename) {
        try {
            return XlsDataWriter.readColumnValues(filename, "Product Name");
        } catch (IOException e) {
            System.err.println("Warning: Could not read existing products from file: " + e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * Filter out products that already exist in the Excel file
     */
    private List<Product> filterDuplicateProducts(List<Product> products, Set<String> existingProductNames) {
        List<Product> newProducts = new ArrayList<>();
        for (Product product : products) {
            String productName = product.name() != null ? product.name().trim() : "";
            if (!existingProductNames.contains(productName)) {
                newProducts.add(product);
            }
        }
        return newProducts;
    }

    private void exportProductsToExcel(List<Product> products, boolean append) {
        try {
            // Define headers for the Excel file
            List<String> headers = Arrays.asList(
                "Product Name",
                "Category",
                "Calories (per 100g)",
                "Proteins",
                "Carbohydrates",
                "Sugar",
                "Fats",
                "Saturated Fats",
                "Unsaturated Fats",
                "Fiber",
                "Salt",
                "Crawled Date"
            );

            // Convert products to data maps
            List<Map<String, Object>> data = new ArrayList<>();
            String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (Product product : products) {
                Map<String, Object> row = new HashMap<>();
                row.put("Product Name", product.name() != null ? product.name() : "");
                row.put("Category", product.category() != null ? product.category() : "");
                row.put("Calories (per 100g)", product.calories() != null ? product.calories() : "");
                row.put("Proteins", product.proteins() != null ? product.proteins() : "");
                row.put("Carbohydrates", product.carbohydrates() != null ? product.carbohydrates() : "");
                row.put("Sugar", product.sugar() != null ? product.sugar() : "");
                row.put("Fats", product.fats() != null ? product.fats() : "");
                row.put("Saturated Fats", product.saturatedFats() != null ? product.saturatedFats() : "");
                row.put("Unsaturated Fats", product.unsaturatedFats() != null ? product.unsaturatedFats() : "");
                row.put("Fiber", product.fiber() != null ? product.fiber() : "");
                row.put("Salt", product.salt() != null ? product.salt() : "");
                row.put("Crawled Date", currentDateTime);
                data.add(row);
            }

            // Create filename
            String filename = "backend/nutrition-master/nutrition-web/src/main/resources/eatntrack_products.xlsx";

            // Write data to Excel file
            XlsDataWriter.writeData(filename, headers, data, append);

            System.out.println("✅ Successfully exported " + products.size() + " products to: " + filename);

        } catch (IOException e) {
            System.err.println("❌ Failed to export products to Excel: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("❌ Unexpected error during export: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
