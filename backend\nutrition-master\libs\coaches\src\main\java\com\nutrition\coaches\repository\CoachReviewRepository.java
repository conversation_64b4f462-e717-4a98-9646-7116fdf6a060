package com.nutrition.coaches.repository;

import com.nutrition.coaches.entity.CoachReview;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CoachReviewRepository extends JpaRepository<CoachReview, String> {

    // Find reviews by coach ID with pagination
    Page<CoachReview> findByCoachIdOrderByCreatedAtDesc(String coachId, Pageable pageable);

    // Find reviews by user ID
    Page<CoachReview> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);

    // Find reviews by coach ID and user ID
    Optional<CoachReview> findByCoachIdAndUserId(String coachId, String userId);

    // Find reviews by rating
    Page<CoachReview> findByRatingOrderByCreatedAtDesc(Integer rating, Pageable pageable);

    // Find reviews by coach ID and rating
    Page<CoachReview> findByCoachIdAndRatingOrderByCreatedAtDesc(String coachId, Integer rating, Pageable pageable);

    // Find top helpful reviews for a coach
    @Query("SELECT r FROM CoachReview r WHERE r.coach.id = :coachId ORDER BY r.helpfulCount DESC, r.createdAt DESC")
    Page<CoachReview> findTopHelpfulReviewsByCoachId(@Param("coachId") String coachId, Pageable pageable);

    // Find recent reviews for a coach
    @Query("SELECT r FROM CoachReview r WHERE r.coach.id = :coachId ORDER BY r.createdAt DESC")
    Page<CoachReview> findRecentReviewsByCoachId(@Param("coachId") String coachId, Pageable pageable);

    // Count total reviews for a coach
    Long countByCoachId(String coachId);

    // Get average rating for a coach
    @Query("SELECT AVG(r.rating) FROM CoachReview r WHERE r.coach.id = :coachId")
    Double getAverageRatingByCoachId(@Param("coachId") String coachId);

    // Get rating distribution for a coach
    @Query("SELECT r.rating, COUNT(r) FROM CoachReview r WHERE r.coach.id = :coachId GROUP BY r.rating ORDER BY r.rating DESC")
    List<Object[]> getRatingDistributionByCoachId(@Param("coachId") String coachId);

    // Find reviews with minimum helpful count
    @Query("SELECT r FROM CoachReview r WHERE r.helpfulCount >= :minHelpfulCount ORDER BY r.helpfulCount DESC, r.createdAt DESC")
    Page<CoachReview> findByMinimumHelpfulCount(@Param("minHelpfulCount") Integer minHelpfulCount, Pageable pageable);

    // Search reviews by comment content
    @Query("SELECT r FROM CoachReview r WHERE LOWER(r.comment) LIKE LOWER(CONCAT('%', :searchTerm, '%')) ORDER BY r.createdAt DESC")
    Page<CoachReview> searchByComment(@Param("searchTerm") String searchTerm, Pageable pageable);

    // Find reviews that can be edited by user
    Page<CoachReview> findByUserIdAndIsEditableOrderByCreatedAtDesc(String userId, Boolean isEditable, Pageable pageable);

    // Find reviews by coach with rating range
    @Query("SELECT r FROM CoachReview r WHERE r.coach.id = :coachId AND r.rating BETWEEN :minRating AND :maxRating ORDER BY r.createdAt DESC")
    Page<CoachReview> findByCoachIdAndRatingRange(
            @Param("coachId") String coachId,
            @Param("minRating") Integer minRating,
            @Param("maxRating") Integer maxRating,
            Pageable pageable
    );

    // Get coaches with most reviews
    @Query("SELECT r.coach.id, COUNT(r) as reviewCount FROM CoachReview r GROUP BY r.coach.id ORDER BY reviewCount DESC")
    List<Object[]> getCoachesByReviewCount(Pageable pageable);
} 