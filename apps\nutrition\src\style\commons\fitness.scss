// Fitness Design System
// Based on Motiff designs: Workout Collections and Workout Detail screens

.fitness {
  // ===== CSS CUSTOM PROPERTIES =====
  
  // Color Palette
  --fitness-accent: #2DE2E6;
  --fitness-accent-rgb: 45, 226, 230;
  --fitness-accent-20: rgba(45, 226, 230, 0.20);
  
  // Backgrounds
  --fitness-bg-primary: #0A1128;
  --fitness-bg-gradient: linear-gradient(180deg, #0A1128 0%, rgba(10, 17, 40, 0.90) 100%);
  --fitness-bg-secondary: #1A2A44;
  --fitness-bg-tertiary: rgba(255, 255, 255, 0.10);
  --fitness-bg-quaternary: rgba(255, 255, 255, 0.20);
  
  // Text Colors
  --fitness-text-primary: #FFFFFF;
  --fitness-text-secondary: #D1D5DB;
  --fitness-text-tertiary: #9CA3AF;
  --fitness-text-accent: #2DE2E6;
  
  // Status Colors
  --fitness-success: #10B981;
  --fitness-warning: #F59E0B;
  --fitness-error: #EF4444;
  --fitness-info: #3B82F6;
  --fitness-gold: #FFD700;
  
  // Overlays
  --fitness-overlay-dark-30: rgba(0, 0, 0, 0.30);
  --fitness-overlay-dark-60: rgba(0, 0, 0, 0.60);
  --fitness-overlay-white-30: rgba(255, 255, 255, 0.30);
  
  // Border & Radius
  --fitness-radius-sm: 4px;
  --fitness-radius-md: 6px;
  --fitness-radius-lg: 8px;
  --fitness-radius-xl: 12px;
  --fitness-radius-pill: 9999px;
  --fitness-border-primary: #2DE2E6;
  --fitness-border-secondary: #2A3A54;
  
  // Spacing
  --fitness-space-1: 4px;
  --fitness-space-2: 8px;
  --fitness-space-3: 12px;
  --fitness-space-4: 16px;
  --fitness-space-5: 20px;
  --fitness-space-6: 24px;
  --fitness-space-7: 28px;
  --fitness-space-8: 32px;
  --fitness-space-10: 40px;
  --fitness-space-12: 48px;
  --fitness-space-16: 64px;
  --fitness-space-20: 80px;
  
  // Shadows
  --fitness-shadow-card: 0px 4px 6px -4px rgba(0, 0, 0, 0.10), 0px 10px 15px -3px rgba(0, 0, 0, 0.10);
  --fitness-shadow-accent: 0px 4px 6px -4px rgba(45, 226, 230, 0.20), 0px 10px 15px -3px rgba(45, 226, 230, 0.20);
  --fitness-shadow-floating: 0px 4px 6px -4px rgba(45, 226, 230, 0.20), 0px 10px 15px -3px rgba(45, 226, 230, 0.20);
  
  // Typography
  --fitness-font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --fitness-font-xs: 12px;
  --fitness-font-sm: 14px;
  --fitness-font-base: 16px;
  --fitness-font-lg: 18px;
  --fitness-font-xl: 20px;
  --fitness-font-2xl: 24px;
  --fitness-font-3xl: 32px;
  
  // Transitions
  --fitness-transition-fast: 0.15s ease-out;
  --fitness-transition-normal: 0.3s ease-out;
  --fitness-transition-slow: 0.5s ease-out;

  // ===== BASE STYLES =====
  
  font-family: var(--fitness-font-family);
  background: var(--fitness-bg-primary);
  color: var(--fitness-text-primary);
  
  // ===== TYPOGRAPHY CLASSES =====
  
  .fitness-display-large {
    font-size: var(--fitness-font-3xl);
    font-weight: 700;
    line-height: 32px;
    color: var(--fitness-text-primary);
  }
  
  .fitness-display-medium {
    font-size: var(--fitness-font-2xl);
    font-weight: 600;
    line-height: 32px;
    color: var(--fitness-text-primary);
  }
  
  .fitness-heading-large {
    font-size: var(--fitness-font-lg);
    font-weight: 600;
    line-height: 28px;
    color: var(--fitness-text-primary);
  }
  
  .fitness-heading-medium {
    font-size: var(--fitness-font-base);
    font-weight: 500;
    line-height: 24px;
    color: var(--fitness-text-primary);
  }
  
  .fitness-body-large {
    font-size: var(--fitness-font-base);
    font-weight: 400;
    line-height: 24px;
    color: var(--fitness-text-secondary);
  }
  
  .fitness-body-medium {
    font-size: var(--fitness-font-sm);
    font-weight: 400;
    line-height: 20px;
    color: var(--fitness-text-secondary);
  }
  
  .fitness-body-small {
    font-size: var(--fitness-font-xs);
    font-weight: 400;
    line-height: 16px;
    color: var(--fitness-text-tertiary);
  }
  
  .fitness-label-medium {
    font-size: var(--fitness-font-sm);
    font-weight: 500;
    line-height: 20px;
    color: var(--fitness-text-primary);
  }
  
  .fitness-label-small {
    font-size: var(--fitness-font-xs);
    font-weight: 600;
    line-height: 16px;
    color: var(--fitness-text-primary);
  }
  
  // ===== BUTTON STYLES =====
  
  .fitness-btn {
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--fitness-transition-normal);
    font-family: var(--fitness-font-family);
    
    &:focus {
      outline: 2px solid var(--fitness-accent);
      outline-offset: 2px;
    }
  }
  
  .fitness-btn-primary {
    @extend .fitness-btn;
    background: var(--fitness-accent);
    color: var(--fitness-bg-primary);
    border-radius: var(--fitness-radius-xl);
    padding: var(--fitness-space-4);
    font-weight: 700;
    font-size: var(--fitness-font-lg);
    line-height: 28px;
    box-shadow: var(--fitness-shadow-accent);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--fitness-shadow-accent), 0px 8px 25px -8px rgba(45, 226, 230, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .fitness-btn-secondary {
    @extend .fitness-btn;
    background: var(--fitness-bg-secondary);
    color: var(--fitness-text-primary);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-2) var(--fitness-space-4);
    font-weight: 500;
    font-size: var(--fitness-font-sm);
    
    &:hover {
      background: lighten(#1A2A44, 5%);
    }
  }
  
  .fitness-btn-icon {
    @extend .fitness-btn;
    background: var(--fitness-bg-secondary);
    color: var(--fitness-text-primary);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-2);
    width: 40px;
    height: 40px;
    
    &:hover {
      background: lighten(#1A2A44, 5%);
    }
  }
  
  .fitness-btn-floating {
    @extend .fitness-btn;
    background: var(--fitness-accent);
    color: var(--fitness-bg-primary);
    border-radius: var(--fitness-radius-pill);
    padding: 14px;
    width: 56px;
    height: 56px;
    box-shadow: var(--fitness-shadow-floating);
    position: fixed;
    bottom: var(--fitness-space-5);
    right: var(--fitness-space-5);
    z-index: 1000;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: var(--fitness-shadow-floating), 0px 8px 25px -8px rgba(45, 226, 230, 0.4);
    }
  }
  
  // ===== CARD STYLES =====
  
  .fitness-card {
    background: var(--fitness-bg-secondary);
    border-radius: var(--fitness-radius-xl);
    padding: var(--fitness-space-4);
    box-shadow: var(--fitness-shadow-card);
    
    &.fitness-card-hover {
      transition: all var(--fitness-transition-normal);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--fitness-shadow-card), var(--fitness-shadow-accent);
      }
    }
  }
  
  .fitness-card-workout {
    @extend .fitness-card;
    overflow: hidden;
    padding: 0;
    
    .fitness-card-image {
      width: 100%;
      height: 160px;
      object-fit: cover;
      position: relative;
    }
    
    .fitness-card-content {
      padding: var(--fitness-space-4);
    }
    
    .fitness-card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--fitness-overlay-dark-30);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: var(--fitness-space-3);
    }
  }
  
  // ===== TAG STYLES =====
  
  .fitness-tag {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    font-family: var(--fitness-font-family);
    transition: all var(--fitness-transition-fast);
    
    &:focus {
      outline: 2px solid var(--fitness-accent);
      outline-offset: 2px;
    }
  }
  
  .fitness-tag-primary {
    @extend .fitness-tag;
    background: var(--fitness-bg-tertiary);
    color: var(--fitness-text-primary);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-3) var(--fitness-space-5);
    font-size: var(--fitness-font-sm);
    
    &:hover {
      background: var(--fitness-bg-quaternary);
    }
  }
  
  .fitness-tag-selected {
    @extend .fitness-tag;
    background: var(--fitness-bg-quaternary);
    color: var(--fitness-text-accent);
    border: 2px solid var(--fitness-accent);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-3) var(--fitness-space-5);
    font-size: var(--fitness-font-sm);
  }
  
  .fitness-tag-muscle {
    @extend .fitness-tag;
    background: var(--fitness-accent-20);
    color: var(--fitness-text-accent);
    border: 1px solid var(--fitness-accent);
    border-radius: var(--fitness-radius-pill);
    padding: calc(var(--fitness-space-2) + 1px) var(--fitness-space-3);
    font-size: var(--fitness-font-sm);
    gap: var(--fitness-space-1);
  }
  
  .fitness-tag-difficulty {
    @extend .fitness-tag;
    background: var(--fitness-accent);
    color: var(--fitness-bg-primary);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-1) var(--fitness-space-3);
    font-size: var(--fitness-font-xs);
    font-weight: 600;
  }
  
  .fitness-tag-workout-type {
    @extend .fitness-tag;
    background: var(--fitness-bg-secondary);
    color: var(--fitness-text-primary);
    border-radius: var(--fitness-radius-lg);
    padding: var(--fitness-space-2) var(--fitness-space-3);
    font-size: var(--fitness-font-sm);
  }
  
  // ===== NAVIGATION STYLES =====
  
  .fitness-nav-bottom {
    background: var(--fitness-bg-secondary);
    border-top: 1px solid var(--fitness-border-secondary);
    padding: 5.5px 14.97px 9.3px 14.78px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 29px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }
  
  .fitness-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--fitness-space-1);
    color: var(--fitness-text-tertiary);
    text-decoration: none;
    font-size: var(--fitness-font-xs);
    font-weight: 400;
    transition: color var(--fitness-transition-fast);
    
    &.active {
      color: var(--fitness-text-accent);
    }
    
    &:hover {
      color: var(--fitness-text-primary);
    }
    
    ion-icon {
      font-size: 24px;
    }
    
    .fitness-nav-indicator {
      width: 6px;
      height: 6px;
      background: var(--fitness-accent);
      border-radius: 50%;
      margin-top: var(--fitness-space-1);
    }
  }
  
  // ===== STATS CARD STYLES =====
  
  .fitness-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--fitness-space-2);
  }
  
  .fitness-stats-card {
    background: var(--fitness-bg-secondary);
    border-radius: var(--fitness-radius-xl);
    padding: var(--fitness-space-3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--fitness-space-1);
    
    ion-icon {
      color: var(--fitness-accent);
      font-size: 24px;
    }
    
    .fitness-stats-value {
      color: var(--fitness-text-primary);
      font-weight: 500;
      font-size: var(--fitness-font-sm);
      line-height: 20px;
      text-align: center;
    }
  }
  
  // ===== SEARCH STYLES =====
  
  .fitness-search {
    background: var(--fitness-bg-secondary);
    border-radius: var(--fitness-radius-pill);
    padding: var(--fitness-space-2) var(--fitness-space-4);
    display: flex;
    align-items: center;
    gap: var(--fitness-space-2);
    border: none;
    
    input {
      background: transparent;
      border: none;
      color: var(--fitness-text-primary);
      font-size: var(--fitness-font-sm);
      flex: 1;
      
      &::placeholder {
        color: var(--fitness-text-tertiary);
      }
      
      &:focus {
        outline: none;
      }
    }
    
    ion-icon {
      color: var(--fitness-text-tertiary);
      font-size: 18px;
    }
  }
  
  // ===== LAYOUT UTILITIES =====
  
  .fitness-container {
    max-width: 390px;
    margin: 0 auto;
    padding: 0 var(--fitness-space-5);
  }
  
  .fitness-section {
    margin-bottom: var(--fitness-space-6);
  }
  
  .fitness-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--fitness-space-2);
  }
  
  .fitness-flex-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: var(--fitness-space-2);
  }
  
  .fitness-space-y-4 > * + * {
    margin-top: var(--fitness-space-4);
  }
  
  .fitness-space-y-6 > * + * {
    margin-top: var(--fitness-space-6);
  }
  
  // ===== BACKGROUND UTILITIES =====
  
  .fitness-bg-gradient {
    background: var(--fitness-bg-gradient);
  }
  
  .fitness-bg-primary {
    background: var(--fitness-bg-primary);
  }
  
  .fitness-bg-secondary {
    background: var(--fitness-bg-secondary);
  }
  
  // ===== TEXT UTILITIES =====
  
  .fitness-text-accent {
    color: var(--fitness-text-accent) !important;
  }
  
  .fitness-text-primary {
    color: var(--fitness-text-primary) !important;
  }
  
  .fitness-text-secondary {
    color: var(--fitness-text-secondary) !important;
  }
  
  .fitness-text-tertiary {
    color: var(--fitness-text-tertiary) !important;
  }
  
  // ===== RESPONSIVE DESIGN =====
  
  @media (min-width: 768px) {
    .fitness-container {
      max-width: 768px;
      padding: 0 var(--fitness-space-8);
    }
    
    .fitness-stats-grid {
      grid-template-columns: repeat(6, 1fr);
    }
    
    .fitness-grid-2 {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @media (min-width: 1024px) {
    .fitness-container {
      max-width: 1024px;
    }
    
    .fitness-stats-grid {
      grid-template-columns: repeat(8, 1fr);
    }
    
    .fitness-grid-2 {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  // ===== ANIMATION CLASSES =====
  
  .fitness-animate-fade-in {
    animation: fitnessSlideInUp 0.3s ease-out;
  }
  
  .fitness-animate-slide-up {
    animation: fitnessSlideInUp 0.5s ease-out;
  }
  
  @keyframes fitnessFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes fitnessSlideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  // ===== IONIC COMPONENT OVERRIDES =====
  
  // Override Ionic components to match fitness theme
  ion-toolbar {
    --background: var(--fitness-bg-primary);
    --color: var(--fitness-text-primary);
    --border-color: transparent;
  }
  
  ion-content {
    --background: var(--fitness-bg-primary);
    --color: var(--fitness-text-primary);
  }
  
  ion-header {
    &::after {
      display: none; // Remove default Ionic header border
    }
  }
  
  ion-tab-bar {
    --background: var(--fitness-bg-secondary);
    --border: 1px solid var(--fitness-border-secondary);
  }
  
  ion-tab-button {
    --color: var(--fitness-text-tertiary);
    --color-selected: var(--fitness-text-accent);
  }
  
  // ===== ACCESSIBILITY =====
  
  .fitness-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  // Focus styles for better accessibility
  .fitness-focusable {
    &:focus-visible {
      outline: 2px solid var(--fitness-accent);
      outline-offset: 2px;
      border-radius: var(--fitness-radius-sm);
    }
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    .fitness {
      --fitness-bg-tertiary: rgba(255, 255, 255, 0.2);
      --fitness-bg-quaternary: rgba(255, 255, 255, 0.3);
      --fitness-text-secondary: #E5E7EB;
      --fitness-text-tertiary: #D1D5DB;
    }
  }
  
  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    .fitness * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
} 