import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';



export interface GlobalState {
  loading: boolean;
  error: string | null;
  title: string ;
  other: Record<string, any>;
}

const initialState: GlobalState = {
  loading: false,
  error: null,
  title: '',
  other: {}
};

export const GlobalStore = signalStore(
  {providedIn: 'root' },
  withState<GlobalState>(initialState),
  withMethods((store)=>({
    setLoading(loading: boolean) {
      patchState(store, {loading});
    },
    setError(error: string | null) {
      patchState(store, {error});
    },
    setTitle(title: string ) {
      patchState(store, {title});
    }
  })
)
);
