-- Schema for H2 database tests
CREATE TABLE IF NOT EXISTS person (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    description CLOB, -- Use CLOB for JSON data in H2
    skills VARCHAR(1000), -- Use VARCHAR for array data in H2 tests  
    languages VARCHAR(1000), -- Use VARCHAR for array data in H2 tests
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP
);

-- Create unique index on email
CREATE UNIQUE INDEX IF NOT EXISTS idx_person_email ON person(email); 