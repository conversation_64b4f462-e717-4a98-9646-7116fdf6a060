import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { API_URL } from '@nutrition/shared';

export interface Video {
  uuid: string;
  title: string;
  videoUrl: string;
  thumbnailUrl: string;
  duration: number;
  status: 'PROCESSING' | 'READY' | 'ERROR';
  originalFilename: string;
  fileSize: number;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface VideoUploadResponse {
  id: string;
  title: string;
  status: 'PROCESSING';
  originalFilename: string;
  fileSize: number;
  createdAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class VideoService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = inject(API_URL);

  /**
   * Fetch all ready videos from the backend
   */
  getVideos(): Observable<Video[]> {
    return this.http.get<Video[]>(`${this.apiUrl}api/videos`);
  }

  /**
   * Fetch a single video by ID with current status
   */
  getVideo(id: string): Observable<Video> {
    return this.http.get<Video>(`${this.apiUrl}api/videos/${id}`);
  }

  /**
   * Upload a video file with title
   */
  uploadVideo(file: File, title: string): Observable<VideoUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', title);

    return this.http.post<VideoUploadResponse>(`${this.apiUrl}api/videos/upload`, formData);
  }

  /**
   * Delete a video (soft delete)
   */
  deleteVideo(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}api/videos/${id}`);
  }

  /**
   * Get all videos (admin endpoint)
   */
  getAllVideos(): Observable<Video[]> {
    return this.http.get<Video[]>(`${this.apiUrl}api/videos/all`);
  }
}
