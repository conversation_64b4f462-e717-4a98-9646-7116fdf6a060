.coaches-content {
  --background: var(--fitness-bg-primary);
  --color: var(--fitness-text-primary);
}

ion-header {
  ion-toolbar {
    --background: var(--fitness-bg-primary);
    --color: var(--fitness-text-primary);
    --border-color: var(--fitness-border);

    ion-title {
      font-size: 28px;
      font-weight: 700;
      color: var(--fitness-text-primary);
    }

    ion-buttons ion-button {
      --color: var(--fitness-text-primary);
      
      .filter-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: var(--fitness-accent);
        color: var(--fitness-bg-primary);
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
      }
    }
  }
}

.coaches-header {
  padding: 16px;
  background: var(--fitness-bg-primary);
  border-bottom: 1px solid var(--fitness-border);

  .search-container {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 16px;

    .coaches-searchbar {
      flex: 1;
      --background: var(--fitness-bg-secondary);
      --color: var(--fitness-text-primary);
      --placeholder-color: var(--fitness-text-secondary);
      --icon-color: var(--fitness-text-secondary);
      --border-radius: 25px;
      --box-shadow: none;
      --padding-start: 20px;
      --padding-end: 20px;
    }

    .filter-fab {
      --background: var(--fitness-accent);
      --color: var(--fitness-bg-primary);
      --border-radius: 25px;
      width: 48px;
      height: 48px;
      margin: 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .quick-filters {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none;
    }

    .quick-filter-chip {
      --background: var(--fitness-bg-secondary);
      --color: var(--fitness-text-secondary);
      border: 1px solid var(--fitness-border);
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
      flex-shrink: 0;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: fit-content;
      padding: 8px 16px;

      &.selected {
        --background: var(--fitness-accent);
        --color: var(--fitness-bg-primary);
        border-color: var(--fitness-accent);
        border-width: 2px;
        box-shadow: 0 0 0 1px var(--fitness-accent);
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.coaches-list {
  padding: 0 16px;

  .coaches-virtual-scroll {
    height: calc(100vh - 280px); // Adjust based on header height
  }
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;

  ion-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  h3 {
    color: var(--fitness-text-primary);
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }

  p {
    color: var(--fitness-text-secondary);
    font-size: 16px;
    margin: 0 0 24px 0;
    max-width: 300px;
  }

  ion-button {
    --border-color: var(--fitness-accent);
    --color: var(--fitness-accent);
  }
}

.loading-container {
  ion-spinner {
    --color: var(--fitness-accent);
    margin-bottom: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .coaches-header {
    .search-container {
      .filter-fab {
        display: none; // Hide on mobile, use header button instead
      }
    }
  }
}

// Dark theme adjustments for fitness theme
:host-context(body.theme-fitness) {
  .coaches-content {
    --background: var(--fitness-bg-primary);
  }
}
