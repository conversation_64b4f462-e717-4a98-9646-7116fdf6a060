.explorer-container{
  overflow-y: auto;
  .top-part{
    ion-toolbar{
      
      
        
      ion-button::part(native){
        --padding-start: 0px;
        --padding-bottom: 0px;
        --padding-end: 0px;
        --padding-top: 0px;
      }
      --background: none !important;
      .toolbar-background{
        --background: none !important;
      }
    }
    ion-toolbar.ion-color{
     --ion-color-base: transparent !important;
    }
    .circle-card {
        width: 100%;
        border-radius: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
      
        .info-section {
          width: 100%;
          padding: 16px;
          padding-top: 0;
          text-align: center;
          color: #fff;
        }
      }
  
      lib-circle-wrapper {
        width: 100%;
        height: 100%;
      }
  }
}