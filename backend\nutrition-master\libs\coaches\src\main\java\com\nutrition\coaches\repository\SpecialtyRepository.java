package com.nutrition.coaches.repository;

import com.nutrition.coaches.entity.Specialty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SpecialtyRepository extends JpaRepository<Specialty, String> {

    // Find specialty by name
    Optional<Specialty> findByName(String name);

    // Check if specialty exists by name
    boolean existsByName(String name);
} 