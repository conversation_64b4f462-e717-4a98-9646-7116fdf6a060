.coach-details-content {
  --background: var(--ion-color-dark);
  --color: var(--ion-color-light);
}

.coach-details {
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.coach-header {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;

  .coach-avatar {
    flex-shrink: 0;

    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--ion-color-primary);
    }
  }

  .coach-info {
    flex: 1;

    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--ion-color-light);
    }

    .coach-rating {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 8px;

      .star-icon {
        color: var(--ion-color-warning);
        font-size: 16px;
      }

      span {
        font-size: 14px;
        color: var(--ion-color-medium);
      }

      .review-count {
        margin-left: 4px;
      }
    }

    .coach-location {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--ion-color-medium);
      font-size: 14px;

      ion-icon {
        font-size: 16px;
      }
    }
  }
}

.coach-stats {
  display: flex;
  justify-content: space-around;
  background: var(--ion-color-dark-shade);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;

  .stat-item {
    text-align: center;

    .stat-value {
      display: block;
      font-size: 20px;
      font-weight: 600;
      color: var(--ion-color-primary);
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: var(--ion-color-medium);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.coach-section {
  margin-bottom: 24px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-light);
    margin: 0 0 12px 0;
  }

  p {
    color: var(--ion-color-medium);
    line-height: 1.6;
    margin: 0;
  }
}

.specialties-grid,
.goals-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .specialty-chip,
  .goal-chip {
    --background: var(--ion-color-primary-shade);
    --color: var(--ion-color-primary-contrast);
    font-size: 12px;
  }
}

.certifications-list {
  .certification-item {
    background: var(--ion-color-dark-shade);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;

    h3 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-light);
    }

    p {
      margin: 0 0 4px 0;
      font-size: 12px;
      color: var(--ion-color-medium);
    }

    .cert-year {
      font-size: 11px;
      color: var(--ion-color-primary);
      font-weight: 500;
    }
  }
}

.action-buttons {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .book-button {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primary-contrast);
    font-weight: 600;
  }

  .message-button {
    --border-color: var(--ion-color-primary);
    --color: var(--ion-color-primary);
  }

  .reviews-button {
    --color: var(--ion-color-medium);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;

  ion-spinner {
    --color: var(--ion-color-primary);
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}
