<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Filters</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" color="medium" (click)="resetFilters()">
        Reset
      </ion-button>
      <ion-button fill="solid" color="primary" (click)="applyFilters()">
        Apply
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="filter-content">
  <!-- Experience Level -->
  <div class="filter-section">
    <div class="section-header" (click)="toggleSection('experienceLevel')">
      <h3>Experience Level</h3>
      <ion-icon 
        [name]="getSectionExpandState('experienceLevel') ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="getSectionExpandState('experienceLevel')">
      <div class="chips-container">
        <ion-chip
          *ngFor="let level of experienceLevels"
          [class.selected]="isExperienceLevelSelected(level)"
          (click)="toggleExperienceLevel(level)"
          class="filter-chip">
          {{ level }}
        </ion-chip>
      </div>
    </div>
  </div>

  <!-- Specialties -->
  <div class="filter-section">
    <div class="section-header" (click)="toggleSection('specialties')">
      <h3>Specialties</h3>
      <ion-icon 
        [name]="getSectionExpandState('specialties') ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="getSectionExpandState('specialties')">
      <div class="chips-container">
        <ion-chip
          *ngFor="let goal of targetGoals"
          [class.selected]="isTargetGoalSelected(goal.id)"
          (click)="toggleTargetGoal(goal.id)"
          class="filter-chip">
          {{ goal.name }}
        </ion-chip>
      </div>
    </div>
  </div>

  <!-- Availability -->
  <div class="filter-section">
    <div class="section-header" (click)="toggleSection('availability')">
      <h3>Availability</h3>
      <ion-icon 
        [name]="getSectionExpandState('availability') ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="getSectionExpandState('availability')">
      <div class="chips-container">
        <ion-chip
          *ngFor="let env of environments"
          [class.selected]="isEnvironmentSelected(env.id)"
          (click)="toggleEnvironment(env.id)"
          class="filter-chip">
          {{ env.name }}
        </ion-chip>
      </div>
    </div>
  </div>

  <!-- Rating Sort -->
  <div class="filter-section">
    <div class="section-header" (click)="toggleSection('rating')">
      <h3>Rating</h3>
      <ion-icon 
        [name]="getSectionExpandState('rating') ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="getSectionExpandState('rating')">
      <ion-radio-group [(ngModel)]="sortOption" (ionChange)="onSortChange()">
        <div class="radio-item" (click)="setSortOption('rating-desc')">
          <ion-radio value="rating-desc"></ion-radio>
          <span>High to Low</span>
        </div>
        <div class="radio-item" (click)="setSortOption('rating-asc')">
          <ion-radio value="rating-asc"></ion-radio>
          <span>Low to High</span>
        </div>
      </ion-radio-group>
    </div>
  </div>

  <!-- Languages -->
  <div class="filter-section">
    <div class="section-header" (click)="toggleSection('languages')">
      <h3>Languages</h3>
      <ion-icon 
        [name]="getSectionExpandState('languages') ? 'chevron-up-outline' : 'chevron-down-outline'"
        class="expand-icon">
      </ion-icon>
    </div>
    
    <div class="section-content" [class.expanded]="getSectionExpandState('languages')">
      <div class="chips-container">
        <ion-chip
          *ngFor="let lang of languages"
          [class.selected]="isLanguageSelected(lang)"
          (click)="toggleLanguage(lang)"
          class="filter-chip">
          {{ lang }}
        </ion-chip>
      </div>
    </div>
  </div>
</ion-content>
