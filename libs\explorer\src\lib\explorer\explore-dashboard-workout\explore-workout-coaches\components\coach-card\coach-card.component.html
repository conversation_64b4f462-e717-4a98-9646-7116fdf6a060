<ion-card class="coach-card" button="true">
  <div class="card-content">
    <!-- Coach <PERSON><PERSON> and Basic Info -->
    <div class="coach-main">
      <div class="avatar-container">
        <img
          [src]="coach().avatar"
          [alt]="coach().name"
          class="coach-avatar"
          loading="lazy">
      </div>

      <div class="coach-info">
        <div class="name-rating">
          <h3 class="coach-name">{{ coach().name }}</h3>
          <div class="rating-container">
            <span class="rating-text">{{ coach().rating }}</span>
            <ion-icon name="star" class="star-icon"></ion-icon>
          </div>
        </div>

        <!-- Target Goals Chips -->
        <div class="goals-chips" *ngIf="coach().targetGoals?.length">
          <ion-chip
            *ngFor="let goal of getDisplayedGoals(coach().targetGoals)"
            class="goal-chip">
            {{ goal.name }}
          </ion-chip>
        </div>
      </div>

      <ion-button
        fill="clear"
        size="small"
        class="favorite-button"
        (click)="onFavoriteClick($event)">
        <ion-icon
          [name]="coach().isFavorite ? 'heart' : 'heart-outline'"
          [class.favorited]="coach().isFavorite">
        </ion-icon>
      </ion-button>
    </div>
  </div>
</ion-card>
