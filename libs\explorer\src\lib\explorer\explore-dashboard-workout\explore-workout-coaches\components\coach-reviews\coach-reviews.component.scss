.reviews-content {
  --background: var(--ion-color-dark);
  --color: var(--ion-color-light);
}

.coach-header {
  background: var(--ion-color-dark-shade);
  padding: 16px;
  border-bottom: 1px solid var(--ion-color-dark-tint);

  .coach-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .coach-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--ion-color-primary);
    }

    .coach-details {
      h2 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-light);
      }

      .coach-rating {
        display: flex;
        align-items: center;
        gap: 4px;

        .star-icon {
          color: var(--ion-color-warning);
          font-size: 14px;
        }

        span {
          font-size: 12px;
          color: var(--ion-color-medium);
        }

        .review-count {
          margin-left: 4px;
        }
      }
    }
  }
}

.reviews-container {
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.reviews-list {
  .review-item {
    background: var(--ion-color-dark-shade);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid var(--ion-color-dark-tint);

    .review-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .reviewer-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .reviewer-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: var(--ion-color-primary);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          color: var(--ion-color-primary-contrast);
          font-size: 16px;
        }

        .reviewer-details {
          h3 {
            margin: 0 0 2px 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--ion-color-light);
          }

          .review-date {
            margin: 0;
            font-size: 12px;
            color: var(--ion-color-medium);
          }
        }
      }

      .review-rating {
        .stars {
          display: flex;
          gap: 2px;

          ion-icon {
            font-size: 14px;
            color: var(--ion-color-medium);

            &.filled {
              color: var(--ion-color-warning);
            }
          }
        }
      }
    }

    .review-content {
      p {
        margin: 0 0 12px 0;
        color: var(--ion-color-light);
        line-height: 1.5;
        font-size: 14px;
      }
    }

    .review-helpful {
      display: flex;
      align-items: center;
      gap: 6px;
      color: var(--ion-color-medium);
      font-size: 12px;

      ion-icon {
        font-size: 14px;
      }
    }
  }
}

.no-reviews {
  text-align: center;
  padding: 48px 16px;
  color: var(--ion-color-medium);

  .no-reviews-icon {
    font-size: 64px;
    color: var(--ion-color-dark-tint);
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: var(--ion-color-light);
  }

  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
  }
}
