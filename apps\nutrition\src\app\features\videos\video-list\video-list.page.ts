import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonList,
  IonItem,
  IonLabel,
  IonThumbnail,
  IonSkeletonText,
  IonIcon,
  IonFab,
  IonFabButton,
  IonRefresher,
  IonRefresherContent,
  IonText,
  IonButton
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { add, playCircle, refresh } from 'ionicons/icons';
import { VideoService, Video } from '../video.service';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-video-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonList,
    IonItem,
    IonLabel,
    IonThumbnail,
    IonSkeletonText,
    IonIcon,
    IonFab,
    IonFabButton,
    IonRefresher,
    IonRefresherContent,
    IonText,
    IonButton
  ],
  template: `
    <ion-header [translucent]="true">
      <ion-toolbar>
        <ion-title>Video Library</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content [fullscreen]="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">Video Library</ion-title>
        </ion-toolbar>
      </ion-header>

      <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
        <ion-refresher-content
          pullingIcon="refresh"
          pullingText="Pull to refresh"
          refreshingSpinner="circles"
          refreshingText="Refreshing...">
        </ion-refresher-content>
      </ion-refresher>

      <!-- Loading State -->
      <ion-list *ngIf="isLoading()">
        <ion-item *ngFor="let item of skeletonItems">
          <ion-thumbnail slot="start">
            <ion-skeleton-text [animated]="true"></ion-skeleton-text>
          </ion-thumbnail>
          <ion-label>
            <h3>
              <ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text>
            </h3>
            <p>
              <ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text>
            </p>
          </ion-label>
        </ion-item>
      </ion-list>

      <!-- Empty State -->
      <div *ngIf="!isLoading() && videos().length === 0" class="empty-state">
        <ion-icon name="play-circle" size="large" color="medium"></ion-icon>
        <ion-text color="medium">
          <h2>No videos yet</h2>
          <p>Upload your first video to get started</p>
        </ion-text>
        <ion-button fill="outline" [routerLink]="['/main-tabs/videos/upload']">
          <ion-icon name="add" slot="start"></ion-icon>
          Upload Video
        </ion-button>
      </div>

      <!-- Video List -->
      <ion-list *ngIf="!isLoading() && videos().length > 0">
        <ion-item
          *ngFor="let video of videos(); trackBy: trackByVideoId"
          [routerLink]="['/main-tabs/videos/player', video.uuid]"
          button
        >
          {{video.thumbnailUrl}}
          <ion-thumbnail slot="start">
            <img
              [src]="video.thumbnailUrl"
              [alt]="video.title"
            />
          </ion-thumbnail>
          <ion-label>
            <h3>{{ video.title }}</h3>
            <p>{{ formatDuration(video.duration) }} • {{ formatFileSize(video.fileSize) }}</p>
            <p>{{ formatDate(video.createdAt) }}</p>
          </ion-label>
          <ion-icon name="play-circle" slot="end" color="primary"></ion-icon>
        </ion-item>
      </ion-list>

      <!-- Floating Action Button -->
      <ion-fab slot="fixed" vertical="bottom" horizontal="end">
        <ion-fab-button [routerLink]="['/main-tabs/videos/upload']">
          <ion-icon name="add"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </ion-content>
  `,
  styles: [`
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;
      text-align: center;
      padding: 2rem;
    }

    .empty-state ion-icon {
      margin-bottom: 1rem;
      font-size: 4rem;
    }

    .empty-state h2 {
      margin: 1rem 0 0.5rem 0;
    }

    .empty-state p {
      margin-bottom: 2rem;
    }

    ion-thumbnail img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  `]
})
export class VideoListPage implements OnInit {
  private readonly videoService = inject(VideoService);

  // Signals for reactive state management
  videos = signal<Video[]>([]);
  isLoading = signal(true);

  // Skeleton loader items
  skeletonItems = Array(6).fill(0);

  constructor() {
    addIcons({ add, playCircle, refresh });
  }

  ngOnInit() {
    this.loadVideos();
  }

  private loadVideos() {
    this.isLoading.set(true);
    this.videoService.getVideos().subscribe({
      next: (videos) => {
        this.videos.set(videos);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading videos:', error);
        this.isLoading.set(false);
      }
    });
  }

  handleRefresh(event: any) {
    this.videoService.getVideos().subscribe({
      next: (videos) => {
        this.videos.set(videos);
        event.target.complete();
      },
      error: (error) => {
        console.error('Error refreshing videos:', error);
        event.target.complete();
      }
    });
  }

  trackByVideoId(index: number, video: Video): string {
    return video.uuid;
  }


  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
}
