import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { toSignal } from '@angular/core/rxjs-interop';
import { switchMap, of } from 'rxjs';

import { CoachesService } from '../../services/coaches.service';
import { CoachReview } from '../../models/coach.model';

@Component({
  selector: 'lib-coach-reviews',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  templateUrl: './coach-reviews.component.html',
  styleUrl: './coach-reviews.component.scss'
})
export class CoachReviewsComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly coachesService = inject(CoachesService);

  // Get reviews data from route params
  reviews = toSignal(
    this.route.params.pipe(
      switchMap(params => {
        const coachId = params['id'];
        return coachId ? this.coachesService.getCoachReviews(coachId) : of([]);
      })
    )
  );

  // Get coach data for header
  coach = toSignal(
    this.route.params.pipe(
      switchMap(params => {
        const coachId = params['id'];
        return coachId ? this.coachesService.getCoachById(coachId) : of(null);
      })
    )
  );

  ngOnInit() {
    // Component initialization
  }

  onBackClick() {
    const coachId = this.coach()?.id;
    if (coachId) {
      this.router.navigate(['/coaches', coachId]);
    } else {
      this.router.navigate(['/coaches']);
    }
  }

  getStarArray(rating: number): boolean[] {
    return Array(5).fill(false).map((_, index) => index < rating);
  }

  formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
}
