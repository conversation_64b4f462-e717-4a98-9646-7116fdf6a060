import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { addIcons } from 'ionicons';
import { 
  heartOutline, 
  heart, 
  starOutline,
  star,
  locationOutline,
  checkmarkCircle
} from 'ionicons/icons';

import { Coach } from '../../models/coach.model';

@Component({
  selector: 'lib-coach-card',
  imports: [CommonModule, IonicModule],
  templateUrl: './coach-card.component.html',
  styleUrl: './coach-card.component.scss',
})
export class CoachCardComponent {
  // Signal-based inputs
  coach = input.required<Coach>();

  // Signal-based outputs
  favoriteToggle = output<Coach>();

  constructor() {
    addIcons({
      heartOutline,
      heart,
      starOutline,
      star,
      locationOutline,
      checkmarkCircle
    });
  }

  onFavoriteClick(event: Event) {
    event.stopPropagation();
    this.favoriteToggle.emit(this.coach());
  }

  getStarArray(rating: number): boolean[] {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const stars: boolean[] = [];
    
    for (let i = 0; i < 5; i++) {
      stars.push(i < fullStars || (i === fullStars && hasHalfStar));
    }
    
    return stars;
  }

  getDisplayedGoals(goals: any[]): any[] {
    return goals.slice(0, 3); // Show max 3 goals
  }

  hasMoreGoals(goals: any[]): boolean {
    return goals.length > 3;
  }

  getMoreGoalsCount(goals: any[]): number {
    return goals.length - 3;
  }
}
