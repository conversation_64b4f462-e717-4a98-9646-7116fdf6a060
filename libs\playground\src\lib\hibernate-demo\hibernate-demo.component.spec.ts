import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { IonicModule } from '@ionic/angular';
import { HibernateDemoComponent } from './hibernate-demo.component';
import { Component } from '@angular/core';

// Mock component for router testing
@Component({
  template: '<div>Mock Playground Component</div>'
})
class MockPlaygroundComponent {}

describe('HibernateDemoComponent', () => {
  let component: HibernateDemoComponent;
  let fixture: ComponentFixture<HibernateDemoComponent>;
  let httpMock: HttpTestingController;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HibernateDemoComponent,
        HttpClientTestingModule,
        RouterTestingModule.withRoutes([
          { path: 'playground', component: MockPlaygroundComponent }
        ]),
        IonicModule.forRoot()
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(HibernateDemoComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default values', () => {
    expect(component.selectedView()).toBe('list');
    expect(component.persons()).toEqual([]);
    expect(component.loading()).toBe(false);
    expect(component.saving()).toBe(false);
  });

  it('should load persons on init', () => {
    const mockPersons = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        description: { hobby: 'Programming', favoriteColor: 'Blue', age: 30, bio: 'Software Engineer' },
        skills: ['Java', 'Angular'],
        languages: ['English', 'Spanish']
      }
    ];

    component.ngOnInit();

    const req = httpMock.expectOne('/api/hibernate-demo/persons');
    expect(req.request.method).toBe('GET');
    req.flush(mockPersons);

    expect(component.persons()).toEqual(mockPersons);
    expect(component.loading()).toBe(false);
  });

  it('should handle view changes correctly', () => {
    const mockEvent = { detail: { value: 'search' } };
    
    component.onViewChange(mockEvent);
    
    expect(component.selectedView()).toBe('search');
  });

  it('should perform search by hobby', () => {
    component.searchFilters.hobby = 'Programming';
    const mockResults = [
      {
        id: 1,
        name: 'Developer',
        email: '<EMAIL>',
        description: { hobby: 'Programming', favoriteColor: 'Blue', age: 28, bio: 'Loves coding' },
        skills: ['Java'],
        languages: ['English']
      }
    ];

    component.performSearch();

    const req = httpMock.expectOne('/api/hibernate-demo/persons/by-hobby/Programming');
    expect(req.request.method).toBe('GET');
    req.flush(mockResults);

    expect(component.searchResults()).toEqual(mockResults);
    expect(component.loading()).toBe(false);
  });

  it('should create a new person', () => {
    component.newPerson = {
      name: 'Test User',
      email: '<EMAIL>',
      description: { hobby: 'Testing', favoriteColor: 'Green', age: 25, bio: 'QA Engineer' },
      skills: [],
      languages: []
    };
    component.skillsInput = 'Testing, QA';
    component.languagesInput = 'English, French';

    const mockCreatedPerson = { ...component.newPerson, id: 1 };

    component.createPerson();

    const req = httpMock.expectOne('/api/hibernate-demo/persons');
    expect(req.request.method).toBe('POST');
    
    // Verify that skills and languages were parsed correctly
    expect(req.request.body.skills).toEqual(['Testing', 'QA']);
    expect(req.request.body.languages).toEqual(['English', 'French']);
    
    req.flush(mockCreatedPerson);

    expect(component.saving()).toBe(false);
    expect(component.message()).toBe('Person created successfully!');
  });

  it('should delete a person', () => {
    const personId = 1;
    
    component.deletePerson(personId);

    const req = httpMock.expectOne('/api/hibernate-demo/persons/1');
    expect(req.request.method).toBe('DELETE');
    req.flush({});

    expect(component.message()).toBe('Person deleted successfully!');
  });

  it('should load statistics', () => {
    const mockCount = 10;
    const mockSkills = ['Java', 'Angular', 'Spring Boot'];
    const mockHobbyStats = { Programming: 5, Reading: 3, Gaming: 2 };

    component.loadStatistics();

    // Expect three HTTP requests for statistics
    const countReq = httpMock.expectOne('/api/hibernate-demo/persons/count');
    expect(countReq.request.method).toBe('GET');
    countReq.flush(mockCount);

    const skillsReq = httpMock.expectOne('/api/hibernate-demo/persons/unique-skills');
    expect(skillsReq.request.method).toBe('GET');
    skillsReq.flush(mockSkills);

    const statsReq = httpMock.expectOne('/api/hibernate-demo/persons/statistics/hobbies');
    expect(statsReq.request.method).toBe('GET');
    statsReq.flush(mockHobbyStats);

    expect(component.totalCount()).toBe(mockCount);
    expect(component.uniqueSkills()).toEqual(mockSkills);
    expect(component.hobbyStats()).toEqual([
      { hobby: 'Programming', count: 5 },
      { hobby: 'Reading', count: 3 },
      { hobby: 'Gaming', count: 2 }
    ]);
  });

  it('should handle HTTP errors gracefully', () => {
    component.ngOnInit();

    const req = httpMock.expectOne('/api/hibernate-demo/persons');
    req.error(new ErrorEvent('Network error'));

    expect(component.loading()).toBe(false);
    expect(component.message()).toBe('Error loading persons');
  });

  it('should reset form after creation', () => {
    component.newPerson.name = 'Test';
    component.skillsInput = 'Java';
    component.languagesInput = 'English';

    component['resetForm']();

    expect(component.newPerson.name).toBe('');
    expect(component.skillsInput).toBe('');
    expect(component.languagesInput).toBe('');
  });
}); 