

// Base styles
.fitness-dashboard {
  padding: 0;
  background-color: var(--gray-100);
  min-height: 100vh;
}

// Header Section
.header-section {
  padding: 16px;
 // background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  //margin-bottom: 20px;
}

.header-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.plan-card,
.goal-card {
  margin: 0;
  box-shadow: var(--shadow-sm);

  ion-card-content {
    padding: 16px;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .card-icon {
    font-size: 20px;
    color: var(--primary-color);
  }

  .card-label {
    font-size: 12px;
    color: var(--medium-color);
    font-weight: 500;
    letter-spacing: 0.5px;
  }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark-color);
  line-height: 1.2;
}

// Section styles
.section {
  padding: 0 16px;

  .section-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--dark-color);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  ion-button {
    --color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
  }
}

// Scrollable containers
.trainers-scroll,
.categories-scroll,
.workouts-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding-bottom: 8px;
  scroll-snap-type: x mandatory;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: var(--gray-200);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--medium-color);
    border-radius: 2px;
  }
}

// Trainer Cards
.trainer-card {
  flex-shrink: 0;
  width: 140px;
  margin: 0;
  //scroll-snap-align: start;

  ion-card-content {
    padding: 16px;
    text-align: center;
  }
}

.trainer-photo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 12px;
  background: var(--gray-200);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.trainer-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 4px;
}

.trainer-specialization {
  font-size: 12px;
  color: var(--medium-color);
  margin-bottom: 8px;
}

.trainer-rating {
  display: flex;
  justify-content: center;
  gap: 2px;

  .star-icon {
    font-size: 14px;
    color: var(--gray-300);

    &.filled {
      color: var(--ion-color-warning);
    }
  }
}

// Category Cards
.category-card {
  flex-shrink: 0;
  width: 120px;
  margin: 0;
}

.equipment-card {
  ion-card-content {
    padding: 16px;
    text-align: center;
  }

  .category-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;

    ion-icon {
      font-size: 20px;
      color: var(--white-color);
    }
  }
}

.body-part-card {
  .category-image {
    height: 80px;
    ion-img {
      height: 60px;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  ion-card-content {
    padding: 12px;
    text-align: center;
  }
}

.category-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 4px;
}

.category-count {
  font-size: 11px;
  color: var(--medium-color);
}

// Workout Cards
.workout-card {
  flex-shrink: 0;
  width: 280px;
  margin: 0;
  ion-card-content {
    justify-content: space-between;
  }
}

.workout-image {
  height: 140px;
  background: var(--gray-200);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .workout-level {
    position: absolute;
    top: 12px;
    right: 12px;
    color: var(--ion-color-light);
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    text-transform: capitalize;
    &.beginner {
      background-color: rgba(var(--ion-color-success-rgb), 0.8);
    }
    &.intermediate {
      background-color: rgba(var(--ion-color-warning-rgb), 0.7);
    } 
    &.advanced {
      background-color: rgba(var(--ion-color-danger-rgb), 0.7);
    }

  }
}

.workout-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 8px;
}

.workout-description {
  font-size: 14px;
  color: var(--medium-color);
  line-height: 1.4;
  margin-bottom: 16px;
}

.workout-details {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.workout-detail {
  display: flex;
  align-items: center;
  gap: 4px;

  ion-icon {
    font-size: 14px;
    color: var(--medium-color);
  }

  span {
    font-size: 12px;
    color: var(--medium-color);
  }
}

.workout-trainer {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 12px;

  ion-icon {
    font-size: 14px;
    color: var(--medium-color);
  }

  span {
    font-size: 12px;
    color: var(--medium-color);
  }
}

.workout-tags {
  display: flex;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;

  .workout-tag {
    --color: var(--ion-color-light);
    height: 24px;
    font-size: 11px;
    font-weight: 500;
    background-color: var(--ion-color-secondary);
    color: var(--ion-color-light);
    padding: 0 8px;
    line-height: 24px;
  }
}

.start-workout-btn {
  --background: var(--primary-color);
  --color: var(--white-color);
  font-weight: 600;
  margin: 0;
}


@media (max-width: 480px) {
  .trainers-scroll,
  .categories-scroll,
  .workouts-scroll {
    gap: 8px;
  }

  .trainer-card {
    width: 110px;

    ion-card-content {
      padding: 12px;
    }
  }

  .category-card {
    width: 90px;
  }

  .workout-card {
    width: 240px;
  }
}

