import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, inject, signal, input, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonSpinner,
  IonText,
  IonButton,
  IonIcon,
  IonProgressBar,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { arrowBack, refresh, alertCircle } from 'ionicons/icons';
import Plyr from 'plyr';
import Hls from 'hls.js';

declare global {
  interface Window {
    Hls: typeof Hls;
  }
}
import { VideoService, Video } from '../video.service';
import { interval, Subscription } from 'rxjs';
import { takeWhile, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-video-player',
  standalone: true,
  imports: [
    CommonModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonSpinner,
    IonText,
    IonButton,
    IonIcon,
    IonProgressBar,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    RouterModule
  ],
  template: `
    <ion-header [translucent]="true">
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button defaultHref="/main-tabs/videos"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ video()?.title || 'Video Player' }}</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content [fullscreen]="true">
      <!-- Loading State -->
      <div *ngIf="isLoading()" class="loading-container">
        <ion-spinner name="circles"></ion-spinner>
        <ion-text color="medium">
          <p>Loading video...</p>
        </ion-text>
      </div>

      <!-- Processing State -->
      <div *ngIf="!isLoading() && video()?.status === 'PROCESSING'" class="processing-container">
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ video()?.title }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <div class="processing-content">
              <ion-spinner name="circles" color="primary"></ion-spinner>
              <ion-text color="medium">
                <h3>Processing Video</h3>
                <p>Your video is being processed and will be available shortly. This usually takes a few minutes.</p>
              </ion-text>
              <ion-progress-bar type="indeterminate" color="primary"></ion-progress-bar>
              <ion-button fill="outline" (click)="checkStatus()">
                <ion-icon name="refresh" slot="start"></ion-icon>
                Check Status
              </ion-button>
            </div>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Error State -->
      <div *ngIf="!isLoading() && video()?.status === 'ERROR'" class="error-container">
        <ion-card color="danger">
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="alert-circle"></ion-icon>
              Processing Error
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-text>
              <p>There was an error processing your video: {{ video()?.title }}</p>
              <p *ngIf="video()?.errorMessage" class="error-message">
                {{ video()?.errorMessage }}
              </p>
            </ion-text>
            <ion-button fill="outline" [routerLink]="['/main-tabs/videos']">
              Back to Videos
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Ready State - Video Player -->
      <div *ngIf="!isLoading() && video()?.status === 'READY'" class="video-container">
        <div class="plyr-container">
          <video
            #videoPlayer
            id="player"
            playsinline
            controls
            [poster]="video()?.thumbnailUrl"
            class="video-player"
            crossorigin="anonymous"
            preload="metadata"
          >
            Your browser does not support the video tag.
          </video>
        </div>
        
        <div class="video-info">
          <ion-card>
            <ion-card-header>
              <ion-card-title>{{ video()?.title }}</ion-card-title>
            </ion-card-header>
            <ion-card-content>
              <ion-text color="medium">
                <p>Duration: {{ formatDuration(video()?.duration || 0) }}</p>
                <p>File Size: {{ formatFileSize(video()?.fileSize || 0) }}</p>
                <p>Uploaded: {{ formatDate(video()?.createdAt || '') }}</p>
              </ion-text>
            </ion-card-content>
          </ion-card>
        </div>
      </div>
    </ion-content>
  `,
  styles: [`
    .loading-container,
    .processing-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;
      padding: 2rem;
    }

    .processing-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 1rem;
    }

    .processing-content ion-spinner {
      margin-bottom: 1rem;
    }

    .processing-content ion-progress-bar {
      width: 100%;
      margin: 1rem 0;
    }

    .error-container ion-card {
      width: 100%;
      max-width: 400px;
    }

    .error-message {
      font-style: italic;
      margin-top: 0.5rem;
    }

    .video-container {
      padding: 1rem;
    }

    .plyr-container {
      position: relative;
      width: 100%;
      background: #000;
      border-radius: 8px;
      overflow: hidden;
    }

    .video-player {
      width: 100%;
      height: auto;
    }

    .video-info {
      margin-top: 1rem;
    }

    /* Plyr customizations */
    .plyr {
      border-radius: 8px;
    }

    .plyr--video {
      background: #000;
    }

    /* Responsive video player */
    @media (min-width: 768px) {
      .video-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }
    }
  `]
})
export class VideoPlayerPage implements OnInit, OnDestroy, AfterViewInit {
  private readonly videoService = inject(VideoService);
  private readonly router = inject(Router);

  // Input from route parameter
  id = input.required<string>();

  // Signals for reactive state management
  video = signal<Video | null>(null);
  isLoading = signal(true);

  // Video player reference
  @ViewChild('videoPlayer', { static: false }) videoPlayerRef?: ElementRef<HTMLVideoElement>;

  private pollingSubscription?: Subscription;
  private player?: Plyr;
  private hls?: Hls;

  constructor() {
    addIcons({ arrowBack, refresh, alertCircle });
  }

  ngOnInit() {
    this.loadVideo();
  }

  ngAfterViewInit() {
    // Setup will be called when video is ready
    // No immediate setup needed as video loading is async
  }

  ngOnDestroy() {
    this.stopPolling();
    this.destroyPlayer();
  }

  private loadVideo() {
    this.isLoading.set(true);
    this.videoService.getVideo(this.id()).subscribe({
      next: (video) => {
        this.video.set(video);
        this.isLoading.set(false);
        
        if (video.status === 'READY') {
          // Wait for view to be initialized
          setTimeout(() => this.setupVideoPlayer(video), 0);
        } else if (video.status === 'PROCESSING') {
          this.startPolling();
        }
      },
      error: (error) => {
        console.error('Error loading video:', error);
        this.isLoading.set(false);
      }
    });
  }

  private setupVideoPlayer(video: Video) {
    if (!this.videoPlayerRef) {
      console.error('Video element not found');
      return;
    }

    const videoElement = this.videoPlayerRef.nativeElement;
    
    console.log('Setting up video player for:', video.title);
    console.log('Video URL:', video.videoUrl);
    console.log('Thumbnail URL:', video.thumbnailUrl);

    // Destroy any existing player first
    this.destroyPlayer();

    // Setup video source first
    if (video.videoUrl && video.videoUrl.includes('.m3u8')) {
      // For HLS streams, we need to set up HLS.js first
      console.log('Detected HLS stream, setting up HLS.js');
      this.setupHLS(video.videoUrl, videoElement);
    } else if (video.videoUrl) {
      // For regular video files, set the source directly
      console.log('Setting regular video source');
      videoElement.src = video.videoUrl;
      
      // Initialize Plyr after source is set
      setTimeout(() => {
        this.initializePlyr(videoElement);
      }, 200);
    } else {
      console.error('No video URL provided');
      this.fallbackToNativePlayer(videoElement);
    }
  }

  private initializePlyr(videoElement: HTMLVideoElement) {
    try {
      // Check if video element has a valid source
      if (!videoElement.src && !videoElement.querySelector('source')) {
        console.error('No video source found, skipping Plyr initialization');
        this.fallbackToNativePlayer(videoElement);
        return;
      }

      console.log('Initializing Plyr with source:', videoElement.src);

      // Initialize Plyr with simplified options
      this.player = new Plyr(videoElement, {
        controls: [
          'play-large',
          'play',
          'progress',
          'current-time',
          'duration',
          'mute',
          'volume',
          'fullscreen'
        ],
        ratio: '16:9',
        fullscreen: {
          enabled: true,
          fallback: true,
          iosNative: false
        },
        storage: {
          enabled: true,
          key: 'plyr'
        },
        tooltips: {
          controls: true,
          seek: true
        },
        debug: false // Disable debug to reduce console noise
      });

      // Add event listeners
      this.player.on('ready', () => {
        console.log('Plyr is ready');
      });

      this.player.on('error', (event) => {
        console.error('Plyr error:', event);
        // Fallback to native controls
        this.fallbackToNativePlayer(videoElement);
      });

      this.player.on('loadedmetadata', () => {
        console.log('Video metadata loaded');
      });

      this.player.on('canplay', () => {
        console.log('Video can start playing');
      });
    } catch (error) {
      console.error('Failed to initialize Plyr:', error);
      // Fallback to native controls
      this.fallbackToNativePlayer(videoElement);
    }
  }

  private fallbackToNativePlayer(videoElement: HTMLVideoElement) {
    console.log('Falling back to native video player');
    
    // If we have Plyr instance, destroy it
    if (this.player) {
      try {
        this.player.destroy();
      } catch (e) {
        console.warn('Error destroying Plyr:', e);
      }
      this.player = undefined;
    }
    
    // If we have HLS, destroy it and set the source directly
    if (this.hls) {
      try {
        this.hls.destroy();
      } catch (e) {
        console.warn('Error destroying HLS:', e);
      }
      this.hls = undefined;
    }
    
    // Enable native controls
    videoElement.controls = true;
    videoElement.style.width = '100%';
    videoElement.style.height = 'auto';
    
    console.log('Native player fallback complete');
  }

  private setupHLS(videoUrl: string, videoElement: HTMLVideoElement) {
    console.log('Setting up HLS for URL:', videoUrl);
    
    // Check if HLS is supported natively
    if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari supports HLS natively
      console.log('Native HLS support detected');
      videoElement.src = videoUrl;
      
      // For native HLS, initialize Plyr after metadata is loaded
      videoElement.addEventListener('loadedmetadata', () => {
        console.log('Native HLS metadata loaded');
        this.initializePlyr(videoElement);
      }, { once: true });
      
      // Fallback timeout in case metadata doesn't load
      setTimeout(() => {
        if (!this.player) {
          console.log('Metadata loading timeout, initializing Plyr anyway');
          this.initializePlyr(videoElement);
        }
      }, 3000);
    } else if (Hls.isSupported()) {
      // Use HLS.js for other browsers
      console.log('Using HLS.js for streaming');
      this.hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: false,
        backBufferLength: 90,
        maxBufferLength: 30,
        maxMaxBufferLength: 600
      });

      this.hls.loadSource(videoUrl);
      this.hls.attachMedia(videoElement);

      this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest loaded, found ' + this.hls!.levels.length + ' quality levels');
        
        // Initialize Plyr now that HLS is ready
        this.initializePlyr(videoElement);
      });

      this.hls.on(Hls.Events.ERROR, (event: any, data: any) => {
        console.error('HLS error:', data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.error('Fatal network error encountered, trying to recover');
              this.hls!.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.error('Fatal media error encountered, trying to recover');
              this.hls!.recoverMediaError();
              break;
            default:
              console.error('Fatal error, cannot recover, falling back to native player');
              this.fallbackToNativePlayer(videoElement);
              break;
          }
        }
      });
    } else {
      console.error('HLS is not supported in this browser, falling back to native player');
      // Try to play the video directly as a fallback
      videoElement.src = videoUrl;
      this.fallbackToNativePlayer(videoElement);
    }
  }

  private destroyPlayer() {
    if (this.hls) {
      this.hls.destroy();
      this.hls = undefined;
    }
    if (this.player) {
      this.player.destroy();
      this.player = undefined;
    }
  }

  private startPolling() {
    this.stopPolling(); // Ensure no existing polling
    
    this.pollingSubscription = interval(5000) // Poll every 5 seconds
      .pipe(
        switchMap(() => this.videoService.getVideo(this.id())),
        takeWhile(video => video.status === 'PROCESSING', true)
      )
      .subscribe({
        next: (video) => {
          this.video.set(video);
          
          if (video.status === 'READY') {
            setTimeout(() => this.setupVideoPlayer(video), 0);
            this.stopPolling();
          } else if (video.status === 'ERROR') {
            this.stopPolling();
          }
        },
        error: (error) => {
          console.error('Error polling video status:', error);
          this.stopPolling();
        }
      });
  }

  private stopPolling() {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = undefined;
    }
  }

  checkStatus() {
    this.loadVideo();
  }

  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
}
