package com.nutrition.coaches.repository;

import com.nutrition.coaches.entity.Environment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EnvironmentRepository extends JpaRepository<Environment, String> {

    // Find environment by name
    Optional<Environment> findByName(String name);

    // Check if environment exists by name
    boolean existsByName(String name);
} 