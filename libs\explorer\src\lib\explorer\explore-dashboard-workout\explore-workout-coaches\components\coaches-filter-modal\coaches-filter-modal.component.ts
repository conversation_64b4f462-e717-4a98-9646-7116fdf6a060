import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { addIcons } from 'ionicons';
import {
  arrowBack,
  chevronDownOutline,
  chevronUpOutline,
  checkboxOutline,
  checkbox,
  starOutline,
  star
} from 'ionicons/icons';

import { CoachesStore } from '../../state/coaches.store';
import {
  CoachFilters,
  TARGET_GOALS,
  ENVIRONMENTS,
  SPECIAL_CONSIDERATIONS,
  EXPERIENCE_LEVELS,
  SortOption
} from '../../models/coach.model';

@Component({
  selector: 'lib-coaches-filter-modal',
  imports: [CommonModule, IonicModule, FormsModule],
  templateUrl: './coaches-filter-modal.component.html',
  styleUrl: './coaches-filter-modal.component.scss',
})
export class CoachesFilterModalComponent implements OnInit {
  private readonly modalController = inject(ModalController);
  private readonly coachesStore = inject(CoachesStore);

  // Local filter state
  localFilters: CoachFilters = {};
  sortOption: SortOption = 'rating-desc';

  // Filter options
  targetGoals = TARGET_GOALS;
  environments = ENVIRONMENTS;
  experienceLevels = EXPERIENCE_LEVELS;
  languages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese'];

  // Section expand states
  expandedSections: Record<string, boolean> = {
    experienceLevel: false,
    specialties: false,
    availability: false,
    rating: false,
    languages: false
  };

  constructor() {
    addIcons({
      arrowBack,
      chevronDownOutline,
      chevronUpOutline,
      checkboxOutline,
      checkbox,
      starOutline,
      star
    });
  }

  ngOnInit() {
    this.initializeFilters();
  }

  private initializeFilters() {
    const currentFilters = this.coachesStore.filters();
    const currentSort = this.coachesStore.sortOption();

    this.localFilters = { ...currentFilters };
    this.sortOption = currentSort;
  }

  // Section management
  toggleSection(sectionId: string) {
    this.expandedSections[sectionId] = !this.expandedSections[sectionId];
  }

  getSectionExpandState(sectionId: string): boolean {
    return this.expandedSections[sectionId] || false;
  }

  // Experience Level methods
  isExperienceLevelSelected(level: string): boolean {
    return this.localFilters.experienceLevel?.includes(level as any) || false;
  }

  toggleExperienceLevel(level: string) {
    const currentLevels = this.localFilters.experienceLevel || [];
    let updatedLevels: string[];
    
    if (currentLevels.includes(level as any)) {
      updatedLevels = currentLevels.filter(l => l !== level);
    } else {
      updatedLevels = [...currentLevels, level];
    }
    
    this.localFilters.experienceLevel = updatedLevels.length ? updatedLevels as any : undefined;
  }

  // Target Goal methods
  isTargetGoalSelected(goalId: string): boolean {
    return this.localFilters.targetGoals?.includes(goalId) || false;
  }

  toggleTargetGoal(goalId: string) {
    const currentGoals = this.localFilters.targetGoals || [];
    let updatedGoals: string[];
    
    if (currentGoals.includes(goalId)) {
      updatedGoals = currentGoals.filter(g => g !== goalId);
    } else {
      updatedGoals = [...currentGoals, goalId];
    }
    
    this.localFilters.targetGoals = updatedGoals.length ? updatedGoals : undefined;
  }

  // Environment methods
  isEnvironmentSelected(envId: string): boolean {
    return this.localFilters.environments?.includes(envId) || false;
  }

  toggleEnvironment(envId: string) {
    const currentEnvs = this.localFilters.environments || [];
    let updatedEnvs: string[];
    
    if (currentEnvs.includes(envId)) {
      updatedEnvs = currentEnvs.filter(e => e !== envId);
    } else {
      updatedEnvs = [...currentEnvs, envId];
    }
    
    this.localFilters.environments = updatedEnvs.length ? updatedEnvs : undefined;
  }

  // Language methods
  isLanguageSelected(language: string): boolean {
    return this.localFilters.languages?.includes(language) || false;
  }

  toggleLanguage(language: string) {
    const currentLangs = this.localFilters.languages || [];
    let updatedLangs: string[];
    
    if (currentLangs.includes(language)) {
      updatedLangs = currentLangs.filter(l => l !== language);
    } else {
      updatedLangs = [...currentLangs, language];
    }
    
    this.localFilters.languages = updatedLangs.length ? updatedLangs : undefined;
  }

  // Sort methods
  setSortOption(option: SortOption) {
    this.sortOption = option;
  }

  onSortChange() {
    // Handle sort change if needed
  }

  applyFilters() {
    this.coachesStore.setFilters(this.localFilters);
    this.coachesStore.setSortOption(this.sortOption);
    this.modalController.dismiss({ filtersApplied: true });
  }

  resetFilters() {
    this.localFilters = {};
    this.sortOption = 'rating-desc';
  }

  closeModal() {
    this.modalController.dismiss();
  }

  getActiveFilterCount(): number {
    let count = 0;

    if (this.localFilters.targetGoals?.length) count += this.localFilters.targetGoals.length;
    if (this.localFilters.environments?.length) count += this.localFilters.environments.length;
    if (this.localFilters.experienceLevel?.length) count += this.localFilters.experienceLevel.length;
    if (this.localFilters.languages?.length) count += this.localFilters.languages.length;

    return count;
  }
}
