package com.nutrition.coaches.repository;

import com.nutrition.coaches.entity.TargetGoal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TargetGoalRepository extends JpaRepository<TargetGoal, String> {

    // Find target goal by name
    Optional<TargetGoal> findByName(String name);

    // Check if target goal exists by name
    boolean existsByName(String name);
} 