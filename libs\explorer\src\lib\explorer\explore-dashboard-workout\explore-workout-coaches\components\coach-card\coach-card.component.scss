.coach-card {
  --background: var(--fitness-bg-secondary);
  --color: var(--fitness-text-primary);
  margin: 8px 0;
  border-radius: 16px;
  box-shadow: var(--fitness-shadow);
  border: 1px solid var(--fitness-border);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 8px 20px -4px rgba(0, 0, 0, 0.15), 0px 20px 25px -5px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

.card-content {
  padding: 16px;
}

.coach-main {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .avatar-container {
    flex-shrink: 0;

    .coach-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--fitness-border);
    }
  }

  .coach-info {
    flex: 1;
    min-width: 0;

    .name-rating {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .coach-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--fitness-text-primary);
        margin: 0;
        line-height: 1.2;
      }

      .rating-container {
        display: flex;
        align-items: center;
        gap: 4px;

        .rating-text {
          font-size: 16px;
          font-weight: 600;
          color: var(--fitness-text-primary);
        }

        .star-icon {
          font-size: 16px;
          color: var(--fitness-accent);
        }
      }
    }

    .goals-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .goal-chip {
        --background: var(--fitness-overlay);
        --color: var(--fitness-text-primary);
        font-size: 11px;
        height: 24px;
        border-radius: 12px;
        padding: 0 8px;
        margin: 0;
      }
    }
  }

  .favorite-button {
    --color: var(--fitness-text-secondary);
    --background: transparent;
    --padding-start: 8px;
    --padding-end: 8px;
    margin: 0;
    min-height: auto;
    flex-shrink: 0;

    ion-icon {
      font-size: 24px;
      transition: all 0.2s ease;

      &.favorited {
        --color: #ff4757;
        color: #ff4757;
      }
    }

    &:hover ion-icon {
      transform: scale(1.1);
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .coach-main {
    .avatar-container .coach-avatar {
      width: 50px;
      height: 50px;
    }

    .coach-info .name-rating .coach-name {
      font-size: 16px;
    }

    .goals-chips .goal-chip {
      font-size: 10px;
      height: 22px;
    }
  }
}

// Dark theme specific adjustments
:host-context(body.theme-fitness) {
  .coach-card {
    --background: var(--fitness-bg-secondary);
    border-color: var(--fitness-border);
  }
}
