package com.nutrition.coaches.repository;

import com.nutrition.coaches.entity.Coach;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface CoachRepository extends JpaRepository<Coach, String> {

    // Find coaches with pagination
    Page<Coach> findAll(Pageable pageable);

    // Find coach by ID with all relationships loaded
    @Query("SELECT c FROM Coach c " +
           "LEFT JOIN FETCH c.targetGoals " +
           "LEFT JOIN FETCH c.specialties " +
           "LEFT JOIN FETCH c.environments " +
           "LEFT JOIN FETCH c.certifications " +
           "WHERE c.id = :id")
    Optional<Coach> findByIdWithAllRelations(@Param("id") String id);

    // Find coaches by target goals
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.targetGoals tg " +
           "WHERE tg.id IN :goalIds")
    Page<Coach> findByTargetGoalIds(@Param("goalIds") List<String> goalIds, Pageable pageable);

    // Find coaches by specialties
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.specialties s " +
           "WHERE s.id IN :specialtyIds")
    Page<Coach> findBySpecialtyIds(@Param("specialtyIds") List<String> specialtyIds, Pageable pageable);

    // Find coaches by environments
    @Query("SELECT DISTINCT c FROM Coach c " +
           "JOIN c.environments e " +
           "WHERE e.id IN :environmentIds")
    Page<Coach> findByEnvironmentIds(@Param("environmentIds") List<String> environmentIds, Pageable pageable);

    // Find coaches by minimum rating
    Page<Coach> findByRatingGreaterThanEqual(BigDecimal minRating, Pageable pageable);

    // Find coaches by location
    @Query("SELECT c FROM Coach c WHERE LOWER(c.location) LIKE LOWER(CONCAT('%', :location, '%'))")
    Page<Coach> findByLocationContainingIgnoreCase(@Param("location") String location, Pageable pageable);

    // Find coaches with experience greater than or equal to specified years
    Page<Coach> findByExperienceGreaterThanEqual(Integer minExperience, Pageable pageable);

    // Find PRO coaches only
    Page<Coach> findByIsPro(Boolean isPro, Pageable pageable);

    // Find favorite coaches
    Page<Coach> findByIsFavorite(Boolean isFavorite, Pageable pageable);

    // Search coaches by name or title
    @Query("SELECT c FROM Coach c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.bio) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Coach> searchByNameTitleOrBio(@Param("searchTerm") String searchTerm, Pageable pageable);

    // Complex filtering query
    @Query("SELECT DISTINCT c FROM Coach c " +
           "LEFT JOIN c.targetGoals tg " +
           "LEFT JOIN c.specialties s " +
           "LEFT JOIN c.environments e " +
           "WHERE (:targetGoalIds IS NULL OR tg.id IN :targetGoalIds) " +
           "AND (:specialtyIds IS NULL OR s.id IN :specialtyIds) " +
           "AND (:environmentIds IS NULL OR e.id IN :environmentIds) " +
           "AND (:minRating IS NULL OR c.rating >= :minRating) " +
           "AND (:minExperience IS NULL OR c.experience >= :minExperience) " +
           "AND (:isPro IS NULL OR c.isPro = :isPro) " +
           "AND (:isFavorite IS NULL OR c.isFavorite = :isFavorite) " +
           "AND (:searchTerm IS NULL OR " +
           "     LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "     LOWER(c.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "     LOWER(c.bio) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Coach> findWithFilters(
            @Param("targetGoalIds") List<String> targetGoalIds,
            @Param("specialtyIds") List<String> specialtyIds,
            @Param("environmentIds") List<String> environmentIds,
            @Param("minRating") BigDecimal minRating,
            @Param("minExperience") Integer minExperience,
            @Param("isPro") Boolean isPro,
            @Param("isFavorite") Boolean isFavorite,
            @Param("searchTerm") String searchTerm,
            Pageable pageable
    );

    // Get top rated coaches
    @Query("SELECT c FROM Coach c ORDER BY c.rating DESC, c.reviewCount DESC")
    Page<Coach> findTopRatedCoaches(Pageable pageable);

    // Get coaches ordered by review count
    @Query("SELECT c FROM Coach c ORDER BY c.reviewCount DESC, c.rating DESC")
    Page<Coach> findMostReviewedCoaches(Pageable pageable);

    // Count coaches by filter criteria
    @Query("SELECT COUNT(DISTINCT c) FROM Coach c " +
           "LEFT JOIN c.targetGoals tg " +
           "LEFT JOIN c.specialties s " +
           "LEFT JOIN c.environments e " +
           "WHERE (:targetGoalIds IS NULL OR tg.id IN :targetGoalIds) " +
           "AND (:specialtyIds IS NULL OR s.id IN :specialtyIds) " +
           "AND (:environmentIds IS NULL OR e.id IN :environmentIds) " +
           "AND (:minRating IS NULL OR c.rating >= :minRating) " +
           "AND (:minExperience IS NULL OR c.experience >= :minExperience) " +
           "AND (:isPro IS NULL OR c.isPro = :isPro) " +
           "AND (:isFavorite IS NULL OR c.isFavorite = :isFavorite)")
    Long countWithFilters(
            @Param("targetGoalIds") List<String> targetGoalIds,
            @Param("specialtyIds") List<String> specialtyIds,
            @Param("environmentIds") List<String> environmentIds,
            @Param("minRating") BigDecimal minRating,
            @Param("minExperience") Integer minExperience,
            @Param("isPro") Boolean isPro,
            @Param("isFavorite") Boolean isFavorite
    );
} 