package com.nutrition.libs.eatntrack.crawler.processor;

import org.openqa.selenium.WebDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import com.nutrition.libs.eatntrack.crawler.model.Product;
import com.nutrition.libs.selenium.WebDriverManagerProvider;


@Service
public class EatNTrackCrawlingProcess {
    private final WebDriverManagerProvider webDriverManagerProvider;
    public static final int MAX_ELEMENT_WAIT = 10;



    @Autowired
    public EatNTrackCrawlingProcess(WebDriverManagerProvider webDriverManagerProvider) {
        this.webDriverManagerProvider = webDriverManagerProvider;
    }

    public List<Product> crawl(WebDriver driver, int startPage, int endPage){

        List<Product> productList = new ArrayList<>();
        for(int i = startPage; i <= endPage; i++){
            System.out.println("Crawling page " + i);
            driver.get(String.format("https://eatntrack.ro/caloriialimente?p=%s&cat=Bauturi", i));
            EatNTrackPageObject eatNTrackPage = new EatNTrackPageObject(MAX_ELEMENT_WAIT, driver);
            List<Product> products = eatNTrackPage.getProductList();
            productList.addAll(products);
        }
        return productList;
    }
}
