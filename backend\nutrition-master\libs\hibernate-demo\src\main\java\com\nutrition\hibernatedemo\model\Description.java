package com.nutrition.hibernatedemo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.Objects;

/**
 * Description class to be stored as JSON in the database
 */
public class Description implements Serializable {

    @NotBlank(message = "<PERSON><PERSON> cannot be blank")
    @JsonProperty("hobby")
    private String hobby;

    @NotBlank(message = "Favorite color cannot be blank")
    @JsonProperty("favoriteColor")
    private String favoriteColor;

    @Min(value = 0, message = "Age must be non-negative")
    @JsonProperty("age")
    private int age;

    @JsonProperty("bio")
    private String bio;

    // Default constructor
    public Description() {}

    // Constructor
    public Description(String hobby, String favoriteColor, int age, String bio) {
        this.hobby = hobby;
        this.favoriteColor = favoriteColor;
        this.age = age;
        this.bio = bio;
    }

    // Getters and setters
    public String getHobby() {
        return hobby;
    }

    public void setHobby(String hobby) {
        this.hobby = hobby;
    }

    public String getFavoriteColor() {
        return favoriteColor;
    }

    public void setFavoriteColor(String favoriteColor) {
        this.favoriteColor = favoriteColor;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Description that = (Description) o;
        return age == that.age &&
                Objects.equals(hobby, that.hobby) &&
                Objects.equals(favoriteColor, that.favoriteColor) &&
                Objects.equals(bio, that.bio);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hobby, favoriteColor, age, bio);
    }

    @Override
    public String toString() {
        return "Description{" +
                "hobby='" + hobby + '\'' +
                ", favoriteColor='" + favoriteColor + '\'' +
                ", age=" + age +
                ", bio='" + bio + '\'' +
                '}';
    }
} 