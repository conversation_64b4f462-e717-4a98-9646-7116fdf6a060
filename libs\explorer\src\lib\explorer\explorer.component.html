<div class="explorer-container"> 
  <div class="top-part" [style.background-color]="headerColor()">
    <ion-toolbar color="primary">
      <ion-buttons slot="end">
        <ion-button shape="round" fill="clear" class="notifications-button" (click)="goToUserProfile()">
          <ion-icon slot="icon-only" name="notifications-outline"></ion-icon>
        </ion-button>
        <ion-button shape="round" fill="clear" class="profile-button" (click)="goToUserProfile()">
          <ion-icon slot="icon-only" name="person-circle-outline"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
    <div class="circle-card" [style.background]="selectedSlice()?.backgroundColor">
      <lib-circle-wrapper (sliceSelected)="onSliceSelected($event)">
      </lib-circle-wrapper>
    </div>
  </div>
  
  <div class="info-section">
      @if(selectedSlice()?.label === 'Nutrition') {
      <lib-explore-dashboard-nutrition
        [backgroundColor]="selectedSlice()?.backgroundColor"></lib-explore-dashboard-nutrition>
      }
      @else if(selectedSlice()?.label === 'Fitness') {
      <lib-explore-dashboard-workout [backgroundColor]="selectedSlice()?.backgroundColor"></lib-explore-dashboard-workout>
      }
      @else {
      <lib-explore-dashboard-mental [backgroundColor]="selectedSlice()?.backgroundColor"></lib-explore-dashboard-mental>
      }
  </div>
</div>