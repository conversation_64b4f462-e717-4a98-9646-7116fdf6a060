.search-header {
  padding: 16px;
  background: var(--ion-color-light);
  border-bottom: 1px solid var(--ion-color-medium-tint);
  
  ion-searchbar {
    margin-bottom: 12px;
    --background: var(--ion-color-white);
    --border-radius: 12px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}


.sort-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;

  .sort-label {
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-right: 8px;
  }

  ion-button {
    --border-radius: 20px;
    font-size: 0.85rem;
    height: 32px;
    
    ion-icon {
      font-size: 16px;
    }
  }
}

.stats {
  display: flex;
  justify-content: center;
  
  ion-chip {
    --background: var(--ion-color-light-shade);
    font-weight: 500;
  }
}

.virtual-scroll-container {
  height: calc(100vh - 280px); // Adjust based on header height
  width: 100%;
}

.virtual-scroll-viewport {
  height: 100%;
  width: 100%;
}

.playground-item {
  --min-height: 120px;
  margin: 8px 16px;
  border-radius: 12px;
  --background: var(--ion-color-white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    --background: var(--ion-color-light-tint);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  ion-avatar {
    .avatar-placeholder {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.2rem;
      text-transform: uppercase;
    }
  }

  ion-label {
    h2 {
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 4px;
      font-size: 1.1rem;
    }

    p {
      color: var(--ion-color-medium-shade);
      font-size: 0.9rem;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .item-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 8px;

      ion-chip {
        height: 24px;
        font-size: 0.75rem;
        
        ion-icon {
          font-size: 14px;
        }
      }
    }

    .date-info {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 0.8rem;
      color: var(--ion-color-medium);
      margin: 0;

      ion-icon {
        font-size: 14px;
      }
    }
  }
}

.error-container,
.empty-state {
  padding: 20px;
  display: flex;
  justify-content: center;
  
  ion-card {
    width: 100%;
    max-width: 400px;
    text-align: center;
    
    ion-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      
      ion-icon {
        font-size: 1.5rem;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .search-header {
    padding: 12px;
  }
  
  .sort-controls {
    justify-content: center;
    
    ion-button {
      font-size: 0.8rem;
      height: 28px;
    }
  }
  
  .playground-item {
    margin: 6px 12px;
    --min-height: 110px;
    
    ion-avatar .avatar-placeholder {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }
    
    ion-label {
      h2 {
        font-size: 1rem;
      }
      
      p {
        font-size: 0.85rem;
      }
    }
  }
  
  .virtual-scroll-container {
    height: calc(100vh - 260px);
  }
}

// Loading animation
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

// Custom scrollbar for webkit browsers
.virtual-scroll-viewport::-webkit-scrollbar {
  width: 6px;
}

.virtual-scroll-viewport::-webkit-scrollbar-track {
  background: var(--ion-color-light);
}

.virtual-scroll-viewport::-webkit-scrollbar-thumb {
  background: var(--ion-color-medium);
  border-radius: 3px;
}

.virtual-scroll-viewport::-webkit-scrollbar-thumb:hover {
  background: var(--ion-color-medium-shade);
} 

// Demo Navigation Styles
.demo-navigation {
  margin: 1rem;
  margin-bottom: 0;
  
  ion-button {
    height: auto;
    min-height: 60px;
    
    ion-icon {
      font-size: 1.5em;
    }
    
    // Ensure text wraps properly
    .button-inner {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      white-space: normal;
      
      ion-icon {
        margin-right: 12px;
        flex-shrink: 0;
      }
    }
  }
  
  // On small screens, make buttons stack vertically
  @media (max-width: 768px) {
    ion-button {
      margin-bottom: 8px;
      
      .button-inner {
        flex-direction: column;
        text-align: center;
        
        ion-icon {
          margin-right: 0;
          margin-bottom: 8px;
        }
      }
    }
  }
} 