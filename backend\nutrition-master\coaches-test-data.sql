-- Coaches Test Data
-- This script populates the database with test data for coaches, their goals, specialties, certifications, and reviews

-- Create coaches table if not exists
CREATE TABLE IF NOT EXISTS coaches (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    title VARCHAR(200),
    avatar VARCHAR(500),
    rating DECIMAL(3,1) NOT NULL DEFAULT 0.0,
    review_count INT NOT NULL DEFAULT 0,
    bio TEXT,
    motto VARCHAR(500),
    experience INT,
    is_pro BOOLEAN DEFAULT FALSE,
    is_favorite BOOLEAN DEFAULT FALSE,
    location VARCHAR(200),
    session_count INT DEFAULT 0,
    price_per_session DECIMAL(8,2),
    video_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create target_goals table
CREATE TABLE IF NOT EXISTS target_goals (
    id VARCHAR(36) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE
);

-- Create coach_target_goals junction table
CREATE TABLE IF NOT EXISTS coach_target_goals (
    coach_id VARCHAR(36),
    goal_id VARCHAR(36),
    level INT DEFAULT 1,
    PRIMARY KEY (coach_id, goal_id),
    FOREIGN KEY (coach_id) REFERENCES coaches(id) ON DELETE CASCADE,
    FOREIGN KEY (goal_id) REFERENCES target_goals(id) ON DELETE CASCADE
);

-- Create specialties table
CREATE TABLE IF NOT EXISTS specialties (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    icon VARCHAR(100)
);

-- Create coach_specialties junction table
CREATE TABLE IF NOT EXISTS coach_specialties (
    coach_id VARCHAR(36),
    specialty_id VARCHAR(36),
    PRIMARY KEY (coach_id, specialty_id),
    FOREIGN KEY (coach_id) REFERENCES coaches(id) ON DELETE CASCADE,
    FOREIGN KEY (specialty_id) REFERENCES specialties(id) ON DELETE CASCADE
);

-- Create certifications table
CREATE TABLE IF NOT EXISTS certifications (
    id VARCHAR(36) PRIMARY KEY,
    coach_id VARCHAR(36),
    name VARCHAR(200) NOT NULL,
    organization VARCHAR(200) NOT NULL,
    year INT,
    verified BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (coach_id) REFERENCES coaches(id) ON DELETE CASCADE
);

-- Create environments table
CREATE TABLE IF NOT EXISTS environments (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    icon VARCHAR(100)
);

-- Create coach_environments junction table
CREATE TABLE IF NOT EXISTS coach_environments (
    coach_id VARCHAR(36),
    environment_id VARCHAR(36),
    PRIMARY KEY (coach_id, environment_id),
    FOREIGN KEY (coach_id) REFERENCES coaches(id) ON DELETE CASCADE,
    FOREIGN KEY (environment_id) REFERENCES environments(id) ON DELETE CASCADE
);

-- Create coach_reviews table
CREATE TABLE IF NOT EXISTS coach_reviews (
    id VARCHAR(36) PRIMARY KEY,
    coach_id VARCHAR(36),
    user_id VARCHAR(36),
    user_name VARCHAR(100),
    reviewer_name VARCHAR(100),
    user_avatar VARCHAR(500),
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 10),
    comment TEXT,
    helpful_count INT DEFAULT 0,
    is_editable BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coach_id) REFERENCES coaches(id) ON DELETE CASCADE
);

-- Insert target goals
INSERT INTO target_goals (id, name) VALUES
('muscle-gain', 'Muscle Gain'),
('weight-loss', 'Weight Loss'),
('flexibility', 'Flexibility Improvement'),
('rehabilitation', 'Rehabilitation'),
('mobility', 'Mobility Enhancement'),
('relaxation', 'Relaxation & Stress Relief'),
('endurance', 'Endurance'),
('posture', 'Posture Correction')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- Insert specialties
INSERT INTO specialties (id, name, icon) VALUES
('weight-loss', 'Weight Loss', 'fitness-outline'),
('muscle-gain', 'Muscle Gain', 'barbell-outline'),
('rehabilitation', 'Rehabilitation', 'medical-outline'),
('flexibility', 'Flexibility', 'body-outline'),
('relaxation', 'Relaxation', 'leaf-outline'),
('endurance', 'Endurance', 'bicycle-outline'),
('posture', 'Posture Correction', 'accessibility-outline')
ON DUPLICATE KEY UPDATE name = VALUES(name), icon = VALUES(icon);

-- Insert environments
INSERT INTO environments (id, name, icon) VALUES
('gym', 'Gym', 'fitness-outline'),
('home', 'Home', 'home-outline'),
('office', 'Office', 'business-outline'),
('outdoor', 'Outdoor', 'leaf-outline'),
('limited-space', 'Limited Space', 'contract-outline')
ON DUPLICATE KEY UPDATE name = VALUES(name), icon = VALUES(icon);

-- Insert coaches
INSERT INTO coaches (id, name, title, avatar, rating, review_count, bio, motto, experience, is_pro, is_favorite, location, session_count, price_per_session) VALUES
('1', 'Sarah M.', 'Certified Personal Trainer', 'https://static.motiffcontent.com/private/resource/image/197d1db92b5f69f-23d664c4-058b-4862-9ed1-91593a6e576b.jpeg', 9.5, 124, 'Professional strength coach specialized in powerlifting and functional training with 10+ years of experience helping clients achieve their fitness goals. Certified in advanced training techniques and nutrition planning.', 'Your only limit is the one you set yourself', 10, TRUE, TRUE, 'New York, NY', 1250, 85.00),

('2', 'John D.', 'Fitness Specialist', 'https://static.motiffcontent.com/private/resource/image/197ccb28013e2a7-7c4b5928-11fb-453b-a510-db9067991bc3.jpeg', 8.5, 89, 'Experienced fitness trainer specializing in functional movement and injury prevention with a focus on helping clients build sustainable healthy habits.', 'Progress, not perfection', 7, FALSE, FALSE, 'Los Angeles, CA', 890, 65.00),

('3', 'Emily R.', 'Yoga & Wellness Coach', 'https://static.motiffcontent.com/private/resource/image/197ccb28013e2a7-7c4b5928-11fb-453b-a510-db9067991bc3.jpeg', 10.0, 156, 'Certified yoga instructor and wellness coach focused on mind-body connection. Specializes in stress relief, flexibility, and holistic wellness approaches.', 'Find balance within', 8, TRUE, TRUE, 'San Francisco, CA', 650, 75.00),

('4', 'Marcus T.', 'Strength & Conditioning Coach', 'https://static.motiffcontent.com/private/resource/image/197d1db92b98e47-156a3a41-0682-4d96-b10a-c3f4022e94c2.jpeg', 9.2, 67, 'Former college athlete turned strength coach. Specializes in athletic performance and sports conditioning for athletes of all levels.', 'Train like a champion', 6, TRUE, FALSE, 'Chicago, IL', 420, 90.00),

('5', 'Lisa K.', 'Rehabilitation Specialist', 'https://static.motiffcontent.com/private/resource/image/197d1db92b9650e-3144cdc5-81f4-4516-9799-7f7e70751824.jpeg', 8.8, 93, 'Licensed physical therapist and movement specialist. Helps clients recover from injuries and build strength safely and effectively.', 'Healing through movement', 12, TRUE, FALSE, 'Miami, FL', 780, 80.00);

-- Insert coach target goals
INSERT INTO coach_target_goals (coach_id, goal_id, level) VALUES
('1', 'muscle-gain', 5),
('1', 'endurance', 4),
('1', 'flexibility', 3),
('2', 'muscle-gain', 4),
('2', 'endurance', 3),
('2', 'flexibility', 2),
('3', 'flexibility', 5),
('3', 'relaxation', 5),
('3', 'posture', 4),
('4', 'muscle-gain', 5),
('4', 'endurance', 5),
('4', 'flexibility', 3),
('5', 'rehabilitation', 5),
('5', 'mobility', 5),
('5', 'posture', 4);

-- Insert coach specialties
INSERT INTO coach_specialties (coach_id, specialty_id) VALUES
('1', 'weight-loss'),
('1', 'muscle-gain'),
('1', 'rehabilitation'),
('1', 'flexibility'),
('2', 'rehabilitation'),
('2', 'flexibility'),
('3', 'flexibility'),
('3', 'relaxation'),
('4', 'muscle-gain'),
('4', 'endurance'),
('5', 'rehabilitation'),
('5', 'posture');

-- Insert coach environments
INSERT INTO coach_environments (coach_id, environment_id) VALUES
('1', 'gym'),
('1', 'home'),
('2', 'gym'),
('2', 'outdoor'),
('3', 'home'),
('3', 'office'),
('4', 'gym'),
('4', 'outdoor'),
('5', 'gym'),
('5', 'home');

-- Insert certifications
INSERT INTO certifications (id, coach_id, name, organization, year, verified) VALUES
('cert-1', '1', 'NASM Certified', 'National Academy of Sports Medicine', 2018, TRUE),
('cert-2', '1', 'CrossFit Level 2', 'CrossFit Inc.', 2019, TRUE),
('cert-3', '1', 'First Aid Certified', 'American Red Cross', 2023, TRUE),
('cert-4', '2', 'ACE Certified', 'American Council on Exercise', 2020, TRUE),
('cert-5', '3', 'RYT 500', 'Yoga Alliance', 2017, TRUE),
('cert-6', '3', 'Meditation Teacher', 'Mindfulness Institute', 2019, TRUE),
('cert-7', '4', 'CSCS', 'National Strength and Conditioning Association', 2021, TRUE),
('cert-8', '4', 'USA Weightlifting Level 1', 'USA Weightlifting', 2022, TRUE),
('cert-9', '5', 'Doctor of Physical Therapy', 'University of Miami', 2012, TRUE),
('cert-10', '5', 'Certified Strength and Conditioning Specialist', 'NSCA', 2015, TRUE);

-- Insert coach reviews
INSERT INTO coach_reviews (id, coach_id, user_id, user_name, reviewer_name, user_avatar, rating, comment, helpful_count, is_editable, created_at) VALUES
('review-1', '1', 'user1', 'Alex K.', 'Alex K.', 'https://static.motiffcontent.com/private/resource/image/197d1db92b98e47-156a3a41-0682-4d96-b10a-c3f4022e94c2.jpeg', 5, 'Sarah helped me achieve my fitness goals in just 3 months. Her personalized approach made all the difference!', 12, FALSE, DATE_SUB(NOW(), INTERVAL 14 DAY)),

('review-2', '1', 'user2', 'Jessica T.', 'Jessica T.', 'https://static.motiffcontent.com/private/resource/image/197d1db92b9650e-3144cdc5-81f4-4516-9799-7f7e70751824.jpeg', 5, 'The best trainer I''ve ever worked with. Knowledgeable, motivating and truly cares about her clients'' progress.', 8, FALSE, DATE_SUB(NOW(), INTERVAL 30 DAY)),

('review-3', '2', 'user3', 'Michael R.', 'Michael R.', NULL, 4, 'Great functional training approach. John really knows how to prevent injuries while building strength.', 5, FALSE, DATE_SUB(NOW(), INTERVAL 10 DAY)),

('review-4', '3', 'user4', 'Anna S.', 'Anna S.', NULL, 5, 'Emily''s yoga sessions are incredible. She helped me find inner peace and improved my flexibility dramatically.', 15, FALSE, DATE_SUB(NOW(), INTERVAL 7 DAY)),

('review-5', '4', 'user5', 'David L.', 'David L.', NULL, 5, 'Marcus pushed me to levels I never thought possible. His athletic background really shows in his training.', 9, FALSE, DATE_SUB(NOW(), INTERVAL 5 DAY)),

('review-6', '5', 'user6', 'Maria G.', 'Maria G.', NULL, 4, 'Lisa helped me recover from my knee injury and get back to running. Professional and caring approach.', 7, FALSE, DATE_SUB(NOW(), INTERVAL 3 DAY));

-- Update review counts to match inserted reviews
UPDATE coaches SET review_count = (
    SELECT COUNT(*) FROM coach_reviews WHERE coach_reviews.coach_id = coaches.id
);

-- Update ratings based on review averages
UPDATE coaches SET rating = (
    SELECT AVG(rating) FROM coach_reviews WHERE coach_reviews.coach_id = coaches.id
) WHERE id IN (SELECT DISTINCT coach_id FROM coach_reviews); 