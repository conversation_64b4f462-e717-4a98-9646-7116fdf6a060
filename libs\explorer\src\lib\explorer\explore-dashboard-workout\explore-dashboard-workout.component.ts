import { Component, input, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { CustomSvgIconComponent } from '@nutrition/shared';
interface Trainer {
  id: number;
  name: string;
  photo: string;
  specialization: string;
  rating: number;
}

interface ExerciseCategory {
  id: number;
  name: string;
  exerciseCount: number;
  icon: string;
  image?: string;
}

interface Workout {
  id: number;
  title: string;
  level: string;
  description: string;
  duration: number;
  exerciseCount: number;
  trainer: string;
  tags: string[];
  image: string;
}

@Component({
  selector: 'lib-explore-dashboard-workout',
  standalone: true,
  imports: [CommonModule, IonicModule, CustomSvgIconComponent],
  templateUrl: './explore-dashboard-workout.component.html',
  styleUrl: './explore-dashboard-workout.component.scss',
})
export class ExploreDashboardWorkoutComponent {
  
  // Professional trainers data
  trainers = signal<Trainer[]>([
    {
      id: 1,
      name: '<PERSON>',
      photo: 'explorer/workouts/a1.png',
      specialization: 'HIIT & Forță',
      rating: 5
    },
    {
      id: 2,
      name: '<PERSON>',
      photo: 'explorer/workouts/a2.png',
      specialization: 'Yoga & Pilates',
      rating: 5
    },
    {
      id: 3,
      name: '<PERSON>',
      photo: 'explorer/workouts/a4.png',
      specialization: 'CrossFit & Cardio',
      rating: 4
    }
  ]);

  // Exercise categories data
  exerciseCategories = signal<ExerciseCategory[]>([
    {
      id: 1,
      name: 'Greutăți Libere',
      exerciseCount: 32,
      icon: 'weights-outline'
    },
    {
      id: 2,
      name: 'Aparate Fitness',
      exerciseCount: 28,
      icon: 'workout-machines-outline'
    },
    {
      id: 3,
      name: 'Greutate Corporală',
      exerciseCount: 45,
      icon: 'caliesthenics-outline'
    },
    {
      id: 4,
      name: 'Benzi Elastice',
      exerciseCount: 24,
      icon: 'elastic-band-outline'
    }
  ]);

  // Body parts categories for second row
  bodyPartsCategories = signal<ExerciseCategory[]>([
    {
      id: 5,
      name: 'Piept',
      exerciseCount: 18,
      icon: 'man-outline',
      image: 'explorer/workouts/chest.jpg'
    },
    {
      id: 6,
      name: 'Spate',
      exerciseCount: 22,
      icon: 'man-outline',
      image: 'explorer/workouts/back.jpg'
    },
    {
      id: 7,
      name: 'Brațe',
      exerciseCount: 26,
      icon: 'man-outline',
      image: 'explorer/workouts/arms.jpg'
    },
    {
      id: 8,
      name: 'Umeri',
      exerciseCount: 15,
      icon: 'man-outline',
      image: 'explorer/workouts/shoulders.jpg'
    },
    {
      id: 9,
      name: 'Picioare',
      exerciseCount: 34,
      icon: 'man-outline',
      image: 'explorer/workouts/legs.jpg'
    },
    {
      id: 10,
      name: 'Stretching',
      exerciseCount: 20,
      icon: 'man-outline',
      image: 'explorer/workouts/stretching.jpg'
    }
  ]);

  // Recommended workouts data
  recommendedWorkouts = signal<Workout[]>([
    {
      id: 1,
      title: 'Full Body Strength',
      level: 'intermediar',
      description: 'Un antrenament complet care vizează toate grupurile musculare majore pentru dezvoltarea forței în general.',
      duration: 45,
      exerciseCount: 4,
      trainer: 'Alex Johnson',
      tags: ['masă musculară', 'tonificare'],
      image: 'explorer/workouts/w1.png'
    },
    {
      id: 2,
      title: 'HIIT Cardio Blast',
      level: 'avansat',
      description: 'Antrenament cu intervale de înaltă intensitate pentru a îmbunătăți sănătatea cardiovasculară și pentru a arde calorii.',
      duration: 30,
      exerciseCount: 6,
      trainer: 'Maria Ionescu',
      tags: ['cardio', 'pierdere în greutate'],
      image: 'explorer/workouts/w2.png'
    },
    {
      id: 3,
      title: 'Upper Body Focus',
      level: 'începător',
      description: 'Exerciții concentrate pe partea superioară a corpului pentru dezvoltarea pectorilor, spatelui, umerilor și brațelor.',
      duration: 35,
      exerciseCount: 5,
      trainer: 'Andrei Popescu',
      tags: ['upper body', 'forță'],
      image: 'explorer/workouts/w3.png'
    }
  ]);

  backgroundColor = input<string>('#3880ff');

  onTrainerSelect(trainer: Trainer): void {
    console.log('Trainer selected:', trainer);
    // Handle trainer selection logic
  }

  onCategorySelect(category: ExerciseCategory): void {
    console.log('Category selected:', category);
    // Handle category selection logic
  }

  onWorkoutStart(workout: Workout): void {
    console.log('Starting workout:', workout);
    // Handle workout start logic
  }

  onViewAllTrainers(): void {
    console.log('View all trainers');
    // Navigate to trainers page
  }

  onViewAllWorkouts(): void {
    console.log('View all workouts');
    // Navigate to workouts page
  }
}
