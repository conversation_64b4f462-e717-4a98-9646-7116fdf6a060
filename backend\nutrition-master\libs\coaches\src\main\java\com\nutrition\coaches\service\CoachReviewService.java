package com.nutrition.coaches.service;

import com.nutrition.coaches.entity.CoachReview;
import com.nutrition.coaches.repository.CoachReviewRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class CoachReviewService {

    private final CoachReviewRepository reviewRepository;

    @Autowired
    public CoachReviewService(CoachReviewRepository reviewRepository) {
        this.reviewRepository = reviewRepository;
    }

    /**
     * Get reviews by coach ID with pagination
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getReviewsByCoachId(String coachId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByCoachIdOrderByCreatedAtDesc(coachId, pageable);
    }

    /**
     * Get reviews by user ID
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getReviewsByUserId(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * Get review by ID
     */
    @Transactional(readOnly = true)
    public Optional<CoachReview> getReviewById(String id) {
        return reviewRepository.findById(id);
    }

    /**
     * Get review by coach ID and user ID
     */
    @Transactional(readOnly = true)
    public Optional<CoachReview> getReviewByCoachAndUser(String coachId, String userId) {
        return reviewRepository.findByCoachIdAndUserId(coachId, userId);
    }

    /**
     * Create new review
     */
    public CoachReview createReview(CoachReview review) {
        // Generate ID if not provided
        if (review.getId() == null || review.getId().isEmpty()) {
            review.setId(UUID.randomUUID().toString());
        }

        // Set default values
        if (review.getHelpfulCount() == null) {
            review.setHelpfulCount(0);
        }
        if (review.getIsEditable() == null) {
            review.setIsEditable(false);
        }

        return reviewRepository.save(review);
    }

    /**
     * Update existing review
     */
    public CoachReview updateReview(String id, CoachReview updatedReview) {
        Optional<CoachReview> existingReviewOpt = reviewRepository.findById(id);
        if (existingReviewOpt.isPresent()) {
            CoachReview existingReview = existingReviewOpt.get();

            // Update fields
            if (updatedReview.getRating() != null) {
                existingReview.setRating(updatedReview.getRating());
            }
            if (updatedReview.getComment() != null) {
                existingReview.setComment(updatedReview.getComment());
            }
            if (updatedReview.getHelpfulCount() != null) {
                existingReview.setHelpfulCount(updatedReview.getHelpfulCount());
            }
            if (updatedReview.getIsEditable() != null) {
                existingReview.setIsEditable(updatedReview.getIsEditable());
            }

            return reviewRepository.save(existingReview);
        }
        throw new RuntimeException("Review not found with id: " + id);
    }

    /**
     * Delete review
     */
    public void deleteReview(String id) {
        if (reviewRepository.existsById(id)) {
            reviewRepository.deleteById(id);
        } else {
            throw new RuntimeException("Review not found with id: " + id);
        }
    }

    /**
     * Get top helpful reviews for a coach
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getTopHelpfulReviews(String coachId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return reviewRepository.findTopHelpfulReviewsByCoachId(coachId, pageable);
    }

    /**
     * Get recent reviews for a coach
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getRecentReviews(String coachId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return reviewRepository.findRecentReviewsByCoachId(coachId, pageable);
    }

    /**
     * Search reviews by comment
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> searchReviews(String searchTerm, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.searchByComment(searchTerm, pageable);
    }

    /**
     * Get reviews by rating
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getReviewsByRating(Integer rating, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByRatingOrderByCreatedAtDesc(rating, pageable);
    }

    /**
     * Get reviews by coach and rating
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getReviewsByCoachAndRating(String coachId, Integer rating, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByCoachIdAndRatingOrderByCreatedAtDesc(coachId, rating, pageable);
    }

    /**
     * Get reviews by rating range for a coach
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getReviewsByRatingRange(String coachId, Integer minRating, Integer maxRating, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByCoachIdAndRatingRange(coachId, minRating, maxRating, pageable);
    }

    /**
     * Get editable reviews for user
     */
    @Transactional(readOnly = true)
    public Page<CoachReview> getEditableReviewsForUser(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reviewRepository.findByUserIdAndIsEditableOrderByCreatedAtDesc(userId, true, pageable);
    }

    /**
     * Count reviews for a coach
     */
    @Transactional(readOnly = true)
    public Long countReviewsForCoach(String coachId) {
        return reviewRepository.countByCoachId(coachId);
    }

    /**
     * Get average rating for a coach
     */
    @Transactional(readOnly = true)
    public Double getAverageRatingForCoach(String coachId) {
        return reviewRepository.getAverageRatingByCoachId(coachId);
    }

    /**
     * Get rating distribution for a coach
     */
    @Transactional(readOnly = true)
    public List<Object[]> getRatingDistribution(String coachId) {
        return reviewRepository.getRatingDistributionByCoachId(coachId);
    }

    /**
     * Check if review exists
     */
    @Transactional(readOnly = true)
    public boolean reviewExists(String id) {
        return reviewRepository.existsById(id);
    }

    /**
     * Increment helpful count
     */
    public CoachReview incrementHelpfulCount(String reviewId) {
        Optional<CoachReview> reviewOpt = reviewRepository.findById(reviewId);
        if (reviewOpt.isPresent()) {
            CoachReview review = reviewOpt.get();
            review.setHelpfulCount(review.getHelpfulCount() + 1);
            return reviewRepository.save(review);
        }
        throw new RuntimeException("Review not found with id: " + reviewId);
    }

    /**
     * Toggle editable status
     */
    public CoachReview toggleEditableStatus(String reviewId) {
        Optional<CoachReview> reviewOpt = reviewRepository.findById(reviewId);
        if (reviewOpt.isPresent()) {
            CoachReview review = reviewOpt.get();
            review.setIsEditable(!review.getIsEditable());
            return reviewRepository.save(review);
        }
        throw new RuntimeException("Review not found with id: " + reviewId);
    }
} 