import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GlobalStore } from '@nutrition/shared';
import { CircleWrapperComponent } from '@nutrition/shared'; 
import { IonicModule, ModalController } from '@ionic/angular';
import { UserProfileWrapperComponent } from '@nutrition/user-profile';
import { ExploreDashboardWorkoutComponent } from './explore-dashboard-workout/explore-dashboard-workout.component';
import { ExploreDashboardNutritionComponentComponent } from './explore-dashboard-nutrition/explore-dashboard-nutrition.component';
import { ExploreDashboardMentalComponentComponent } from './explore-dashboard-mental/explore-dashboard-mental.component';
import { addIcons } from 'ionicons';
import { menuOutline, personCircleOutline, chatbubbleOutline, notificationsOutline, trendingUpOutline, star, starOutline, timeOutline, listOutline, personOutline, chevronForwardOutline } from 'ionicons/icons';
@Component({
  selector: 'lib-explorer',
  standalone: true,
  imports: [CommonModule, CircleWrapperComponent, IonicModule, ExploreDashboardWorkoutComponent, ExploreDashboardNutritionComponentComponent, ExploreDashboardMentalComponentComponent],
  templateUrl: './explorer.component.html',
  styleUrl: './explorer.component.css',
})
export class ExplorerComponent {
  private globalStore= inject(GlobalStore);
  private modalCtrl = inject(ModalController);  
  
  headerColor = signal('#1b4f41');
  selectedSlice = signal<any>(null);
  constructor() {
    addIcons({ menuOutline, personCircleOutline, chatbubbleOutline, notificationsOutline, trendingUpOutline, star, starOutline, timeOutline, listOutline, personOutline, chevronForwardOutline });
  }

  ionViewWillEnter() {
    this.globalStore.setTitle('Exploreaza');
  }

  onSliceSelected(slice: any) {
    this.selectedSlice.set(slice);
    this.headerColor.set(slice.backgroundColor);
  }

  async goToUserProfile() {
    const modal = await this.modalCtrl.create({
      component: UserProfileWrapperComponent
    });
    await modal.present();
  }

}
