package com.nutrition.hibernatedemo.repository;

import com.nutrition.hibernatedemo.entity.Person;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for Person entity with advanced JSON and Array queries
 */
@Repository
public interface PersonRepository extends JpaRepository<Person, Long> {

    // Find by email (standard JPA method)
    Person findByEmail(String email);

    // Find people by hobby in JSON description
    @Query(
        value = "SELECT * FROM person p WHERE p.description->>'hobby' = :hobby",
        nativeQuery = true
    )
    List<Person> findByHobby(@Param("hobby") String hobby);

    // Find people by multiple hobbies in JSON description
    @Query(
        value = "SELECT * FROM person p WHERE p.description->>'hobby' IN (:hobbies)",
        nativeQuery = true
    )
    List<Person> findByHobbyIn(@Param("hobbies") List<String> hobbies);

    // Find people by age range in JSON description
    @Query(
        value = "SELECT * FROM person p WHERE CAST(p.description->>'age' AS INTEGER) BETWEEN :minAge AND :maxAge",
        nativeQuery = true
    )
    List<Person> findByAgeRange(@Param("minAge") int minAge, @Param("maxAge") int maxAge);

    // Find people by favorite color in JSON description
    @Query(
        value = "SELECT * FROM person p WHERE p.description->>'favoriteColor' = :color",
        nativeQuery = true
    )
    List<Person> findByFavoriteColor(@Param("color") String color);

    // Find people who have a specific skill in the skills array
    @Query(
        value = "SELECT * FROM person p WHERE :skill = ANY(p.skills)",
        nativeQuery = true
    )
    List<Person> findBySkill(@Param("skill") String skill);

    // Find people who have any of the specified skills
    @Query(
        value = "SELECT * FROM person p WHERE p.skills && CAST(:skills AS TEXT[])",
        nativeQuery = true
    )
    List<Person> findByAnySkill(@Param("skills") String[] skills);

    // Find people who speak a specific language
    @Query(
        value = "SELECT * FROM person p WHERE :language = ANY(p.languages)",
        nativeQuery = true
    )
    List<Person> findByLanguage(@Param("language") String language);

    // Find people who speak multiple languages (must have all specified languages)
    @Query(
        value = "SELECT * FROM person p WHERE p.languages @> CAST(:languages AS TEXT[])",
        nativeQuery = true
    )
    List<Person> findByAllLanguages(@Param("languages") String[] languages);

    // Find people by bio keyword in JSON description (case-insensitive)
    @Query(
        value = "SELECT * FROM person p WHERE LOWER(p.description->>'bio') LIKE LOWER(CONCAT('%', :keyword, '%'))",
        nativeQuery = true
    )
    List<Person> findByBioContaining(@Param("keyword") String keyword);

    // Complex query: Find people by hobby and skill combination
    @Query(
        value = "SELECT * FROM person p WHERE p.description->>'hobby' = :hobby AND :skill = ANY(p.skills)",
        nativeQuery = true
    )
    List<Person> findByHobbyAndSkill(@Param("hobby") String hobby, @Param("skill") String skill);

    // Find people with specific minimum number of skills
    @Query(
        value = "SELECT * FROM person p WHERE array_length(p.skills, 1) >= :minSkills",
        nativeQuery = true
    )
    List<Person> findByMinimumSkillCount(@Param("minSkills") int minSkills);

    // Find people by partial JSON data using JSONB contains operator
    @Query(
        value = "SELECT * FROM person p WHERE p.description @> CAST(:jsonData AS jsonb)",
        nativeQuery = true
    )
    List<Person> findByJsonContains(@Param("jsonData") String jsonData);

    // Get statistics: count people by hobby
    @Query(
        value = "SELECT p.description->>'hobby' as hobby, COUNT(*) as count " +
                "FROM person p " +
                "GROUP BY p.description->>'hobby' " +
                "ORDER BY count DESC",
        nativeQuery = true
    )
    List<Object[]> getHobbyStatistics();

    // Get all unique skills across all people
    @Query(
        value = "SELECT DISTINCT unnest(skills) as skill FROM person ORDER BY skill",
        nativeQuery = true
    )
    List<String> getAllUniqueSkills();

    // Find people with exactly matching skill sets
    @Query(
        value = "SELECT * FROM person p1 WHERE EXISTS (" +
                "SELECT 1 FROM person p2 WHERE p2.id = :personId " +
                "AND p1.skills @> p2.skills " +
                "AND p1.skills <@ p2.skills " +
                "AND p1.id != p2.id)",
        nativeQuery = true
    )
    List<Person> findPeopleWithSameSkillsAs(@Param("personId") Long personId);
} 