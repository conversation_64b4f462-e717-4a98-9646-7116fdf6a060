ion-header {
  ion-toolbar {
    --background: var(--fitness-bg-primary);
    --color: var(--fitness-text-primary);
    --border-color: var(--fitness-border);

    ion-title {
      font-size: 18px;
      font-weight: 600;
    }

    ion-buttons ion-button {
      --color: var(--fitness-text-primary);
      
      &[color="primary"] {
        --background: var(--fitness-accent);
        --color: var(--fitness-bg-primary);
        font-weight: 600;
        border-radius: 8px;
        margin-left: 8px;
      }
    }
  }
}

.filter-content {
  --background: var(--fitness-bg-primary);
  --color: var(--fitness-text-primary);
}

.filter-section {
  margin-bottom: 16px;
  border-bottom: 1px solid var(--fitness-border);
  padding-bottom: 16px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--fitness-overlay);
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--fitness-text-primary);
      margin: 0;
    }

    .expand-icon {
      font-size: 20px;
      color: var(--fitness-text-secondary);
      transition: transform 0.2s ease;
    }
  }

  .section-content {
    padding: 0 16px;
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease, padding 0.3s ease;

    &.expanded {
      max-height: 400px;
      padding: 16px;
    }
  }
}

.chips-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .filter-chip {
    --background: var(--fitness-bg-secondary);
    --color: var(--fitness-text-secondary);
    border: 1px solid var(--fitness-border);
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0;
    height: 32px;
    padding: 0 12px;

    &.selected {
      --background: var(--fitness-accent);
      --color: var(--fitness-bg-primary);
      border-color: var(--fitness-accent);
      transform: scale(1.02);
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--fitness-overlay);
    border-radius: 8px;
    padding-left: 8px;
  }

  ion-radio {
    --color: var(--fitness-accent);
    --color-checked: var(--fitness-accent);
  }

  span {
    font-size: 14px;
    color: var(--fitness-text-primary);
    font-weight: 500;
  }
}

ion-radio-group {
  .radio-item:first-child {
    padding-top: 0;
  }
  
  .radio-item:last-child {
    padding-bottom: 0;
  }
}

// Responsive design
@media (max-width: 480px) {
  .chips-container {
    .filter-chip {
      font-size: 13px;
      height: 30px;
      padding: 0 10px;
    }
  }

  .filter-section .section-header h3 {
    font-size: 15px;
  }
}

// Dark theme adjustments
:host-context(body.theme-fitness) {
  ion-header ion-toolbar {
    --background: var(--fitness-bg-primary);
  }

  .filter-content {
    --background: var(--fitness-bg-primary);
  }
}
