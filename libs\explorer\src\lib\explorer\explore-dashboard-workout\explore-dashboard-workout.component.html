<!-- Header Section -->
<div class="header-section" [style.background-color]="backgroundColor()">
  <div class="header-cards">
    <!-- Current Plan Card -->
    <ion-card class="plan-card card-with-transparency">
      <ion-card-content>
        <div class="card-header">
          <ion-icon name="calendar-outline" class="card-icon"></ion-icon>
          <span class="card-label">Plan Curent</span>
        </div>
        <div class="card-title">Scapa de grăsime in 30 de zile</div>
      </ion-card-content>
    </ion-card>

    <!-- Goal Card -->
    <ion-card class="goal-card card-with-transparency">
      <ion-card-content>
        <div class="card-header">
          <ion-icon name="trending-up-outline" class="card-icon"></ion-icon>
          <span class="card-label">Obiectiv</span>
        </div>
        <div class="card-title">Slăbire</div>
      </ion-card-content>
    </ion-card>
  </div>
</div>

<!-- Professional Trainers Section -->
<div class="section">
  <div class="section-header">
    <h2 class="section-title">Antrenori</h2>
    <ion-button fill="clear" size="small" (click)="onViewAllTrainers()">
      <span>Vezi toți</span>
      <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
    </ion-button>
  </div>
  
  <div class="trainers-scroll">
    @for (trainer of trainers(); track trainer.id) {
      <ion-card class="trainer-card" (click)="onTrainerSelect(trainer)">
        <ion-card-content>
          <div class="trainer-photo">
            <ion-avatar>
              <img [src]="trainer.photo" [alt]="trainer.name" />
            </ion-avatar>
          </div>
          <div class="trainer-info">
            <div class="trainer-name">{{ trainer.name }}</div>
            <div class="trainer-specialization">{{ trainer.specialization }}</div>
            <div class="trainer-rating">
              @for (star of [1,2,3,4,5]; track star) {
                <ion-icon 
                  name="star" 
                  [class.filled]="star <= trainer.rating"
                  class="star-icon">
                </ion-icon>
              }
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    }
  </div>
</div>

<!-- Exercise Categories Section -->
<div class="section">
  <div class="section-header">
    <h2 class="section-title">Exerciții</h2>
    <ion-button fill="clear" size="small" (click)="onViewAllWorkouts()">
      <span>Vezi toate</span>
      <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
    </ion-button>
  </div>
  
  <!-- First Row - Equipment Categories -->
  <div class="categories-scroll">
    @for (category of exerciseCategories(); track category.id) {
      <ion-card class="category-card equipment-card" (click)="onCategorySelect(category)">
        <ion-card-content>
          <div class="category-icon">
            <lib-custom-svg-icon class="custom-icon-style" [iconName]="category.icon" [size]="30"></lib-custom-svg-icon>
          </div>
          <div class="category-name">{{ category.name }}</div>
          <div class="category-count">{{ category.exerciseCount }} exerciții</div>
        </ion-card-content>
      </ion-card>
    }
  </div>

  <!-- Second Row - Body Parts Categories -->
  <div class="categories-scroll">
    @for (category of bodyPartsCategories(); track category.id) {
      <ion-card class="category-card body-part-card" (click)="onCategorySelect(category)">
        <div class="category-image">
          <ion-img [src]="category.image" [alt]="category.name" />
        </div>
        <ion-card-content>
          <div class="category-name">{{ category.name }}</div>
          <div class="category-count">{{ category.exerciseCount }} exerciții</div>
        </ion-card-content>
      </ion-card>
    }
  </div>
</div>

<!-- Recommended Workouts Section -->
<div class="section" style="margin-bottom: 100px;">
  <div class="section-header">
    <h2 class="section-title">Antrenamente</h2>
    <ion-button fill="clear" size="small" (click)="onViewAllWorkouts()">
      <span>Vezi toate</span>
      <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
    </ion-button>
  </div>
  
  <div class="workouts-scroll">
    @for (workout of recommendedWorkouts(); track workout.id) {
      <ion-card class="workout-card ion-flex-column">
        <div class="workout-image">
          <ion-img [src]="workout.image" [alt]="workout.title" />
            <div class="workout-level br-s" 
              [class.beginner]="workout.level === 'începător'" 
              [class.intermediate]="workout.level === 'intermediar'" 
              [class.advanced]="workout.level === 'avansat'">{{ workout.level }}
            </div>
        </div>
        <ion-card-content class="ion-flex-column ion-flex-grow">
          <div>
            <div class="workout-title">{{ workout.title }}</div>
            <div class="workout-description">{{ workout.description }}</div>
          </div>       
          
          <div>
            <div class="workout-details">
              <div class="workout-detail">
                <ion-icon name="time-outline"></ion-icon>
                <span>{{ workout.duration }} min</span>
              </div>
              <div class="workout-detail">
                <ion-icon name="list-outline"></ion-icon>
                <span>{{ workout.exerciseCount }} exerciții</span>
              </div>
            </div>
            
            
              <div class="workout-trainer">
                <ion-icon name="person-outline"></ion-icon>
                <span> {{ workout.trainer }}</span>
              </div>
              
              <div class="workout-tags">
                @for (tag of workout.tags; track tag) {
                  <span class="workout-tag br-l">
                    {{ tag }}
                  </span>
                }
              </div>
         
          
          <ion-button 
            expand="block" 
            color="primary"
            shape="round"
            class="start-workout-btn"
            (click)="onWorkoutStart(workout)">
            <span>Start</span>
            <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
          </ion-button>
        </div>
        </ion-card-content>
      </ion-card>
    }
  </div>
</div>
