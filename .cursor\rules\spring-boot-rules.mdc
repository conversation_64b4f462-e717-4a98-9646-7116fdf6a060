---
description: Spring boot usage rules, backend tasks
globs:
alwaysApply: false
---

Always use .yml files for configuration
Create an aggregation configuration class
Ex:
```java 
    @EntityScan({"com.nutrition.libs.nutrition"})
    @ComponentScan({"com.nutrition.libs.nutrition"})
    @EnableJpaRepositories({"com.nutrition.libs.nutrition"})
    @Configuration
    public class NutritionConfiguration {
    }
```

- For resolving arguments to specification in the spring controller user this library and the provided MCP for documentation:
```xml
 <dependency>
        <groupId>net.kaczmarzyk</groupId>
        <artifactId>specification-arg-resolver</artifactId>
        <version>${specification-arg-resolver.version}</version>
      </dependency>
```