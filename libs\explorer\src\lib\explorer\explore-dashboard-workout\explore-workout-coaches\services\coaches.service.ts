import { Injectable, inject, signal, computed } from '@angular/core';
import { HttpClient, httpResource } from '@angular/common/http';
import { Observable, of, delay, map, catchError } from 'rxjs';
import { Coach, CoachReview } from '../models/coach.model';

@Injectable({
  providedIn: 'root'
})
export class CoachesService {
  private readonly http = inject(HttpClient);

  // Resource for coaches list with pagination
  private readonly coachesPageSignal = signal(1);
  private readonly coachesPageSizeSignal = signal(20);

  readonly coachesResource = httpResource<Coach[]>(() => ({
    url: '/api/coaches',
    method: 'GET',
    params: {
      page: this.coachesPageSignal().toString(),
      pageSize: this.coachesPageSizeSignal().toString()
    }
  }));

  // Resource for individual coach
  private readonly selectedCoachIdSignal = signal<string | null>(null);

  readonly selectedCoachResource = httpResource<Coach>(() => {
    const coachId = this.selectedCoachIdSignal();
    if (!coachId) return undefined;
    
    return {
      url: `/api/coaches/${coachId}`,
      method: 'GET'
    };
  });

  // Resource for coach reviews
  private readonly reviewsCoachIdSignal = signal<string | null>(null);
  private readonly reviewsPageSignal = signal(1);
  private readonly reviewsPageSizeSignal = signal(10);

  readonly coachReviewsResource = httpResource<CoachReview[]>(() => {
    const coachId = this.reviewsCoachIdSignal();
    if (!coachId) return undefined;

    return {
      url: `/api/coaches/${coachId}/reviews`,
      method: 'GET',
      params: {
        page: this.reviewsPageSignal().toString(),
        pageSize: this.reviewsPageSizeSignal().toString()
      }
    };
  });

  // Computed properties for easy access
  readonly coaches = computed(() => this.coachesResource.value() ?? []);
  readonly isLoadingCoaches = computed(() => this.coachesResource.isLoading());
  readonly coachesError = computed(() => this.coachesResource.error());

  readonly selectedCoach = computed(() => this.selectedCoachResource.value() ?? null);
  readonly isLoadingSelectedCoach = computed(() => this.selectedCoachResource.isLoading());

  readonly coachReviews = computed(() => this.coachReviewsResource.value() ?? []);
  readonly isLoadingReviews = computed(() => this.coachReviewsResource.isLoading());

  // Public methods to control the resources
  getCoaches(page = 1, pageSize = 20): Observable<Coach[]> {
    this.coachesPageSignal.set(page);
    this.coachesPageSizeSignal.set(pageSize);
    
    return new Observable(observer => {
      // Return current value if available
      const currentValue = this.coachesResource.value();
      if (currentValue) {
        observer.next(currentValue);
        observer.complete();
        return;
      }

      // Wait for the resource to load
      const subscription = computed(() => {
        const value = this.coachesResource.value();
        const isLoading = this.coachesResource.isLoading();
        const error = this.coachesResource.error();

        if (!isLoading) {
          if (value) {
            observer.next(value);
            observer.complete();
          } else if (error) {
            observer.error(error);
          }
        }
      });

      // Note: In a real implementation, you'd need to handle subscription cleanup
      // This is a simplified example
    });
  }

  getCoachById(id: string): Observable<Coach | null> {
    this.selectedCoachIdSignal.set(id);
    
    return new Observable(observer => {
      const currentValue = this.selectedCoachResource.value();
      if (currentValue) {
        observer.next(currentValue);
        observer.complete();
        return;
      }

      // Similar pattern as above for waiting on resource
      const subscription = computed(() => {
        const value = this.selectedCoachResource.value();
        const isLoading = this.selectedCoachResource.isLoading();
        const error = this.selectedCoachResource.error();

        if (!isLoading) {
          if (value) {
            observer.next(value);
            observer.complete();
          } else if (error) {
            observer.error(error);
          }
        }
      });
    });
  }

  getCoachReviews(coachId: string, page = 1, pageSize = 10): Observable<CoachReview[]> {
    this.reviewsCoachIdSignal.set(coachId);
    this.reviewsPageSignal.set(page);
    this.reviewsPageSizeSignal.set(pageSize);

    return new Observable(observer => {
      const currentValue = this.coachReviewsResource.value();
      if (currentValue) {
        observer.next(currentValue);
        observer.complete();
        return;
      }

      const subscription = computed(() => {
        const value = this.coachReviewsResource.value();
        const isLoading = this.coachReviewsResource.isLoading();
        const error = this.coachReviewsResource.error();

        if (!isLoading) {
          if (value) {
            observer.next(value);
            observer.complete();
          } else if (error) {
            observer.error(error);
          }
        }
      });
    });
  }

  addReview(coachId: string, review: Omit<CoachReview, 'id' | 'date'>): Observable<CoachReview> {
    return this.http.post<CoachReview>(`/api/coaches/${coachId}/reviews`, review).pipe(
      catchError(error => {
        console.warn('API failed, using mock data:', error);
        // Fallback to mock implementation
        const newReview: CoachReview = {
          ...review,
          id: Date.now().toString(),
          date: new Date()
        };
        return of(newReview).pipe(delay(500));
      })
    );
  }

  updateReview(coachId: string, reviewId: string, review: Partial<CoachReview>): Observable<CoachReview> {
    return this.http.put<CoachReview>(`/api/coaches/${coachId}/reviews/${reviewId}`, review).pipe(
      catchError(error => {
        console.warn('API failed, using mock data:', error);
        // Fallback to mock implementation
        const updatedReview: CoachReview = {
          id: reviewId,
          userId: 'current-user',
          userName: 'Current User',
          reviewerName: 'Current User',
          rating: review.rating || 5,
          comment: review.comment || '',
          date: new Date(),
          helpfulCount: 0,
          isEditable: true
        };
        return of(updatedReview).pipe(delay(500));
      })
    );
  }

  toggleFavorite(coachId: string): Observable<boolean> {
    return this.http.post<{isFavorite: boolean}>(`/api/coaches/${coachId}/favorite`, {}).pipe(
      map(response => response.isFavorite),
      catchError(error => {
        console.warn('API failed, using mock data:', error);
        return of(true).pipe(delay(300));
      })
    );
  }

  // Utility methods for refreshing resources
  refreshCoaches(): void {
    // Force refresh by updating the signal
    this.coachesPageSignal.update(page => page);
  }

  refreshSelectedCoach(): void {
    this.selectedCoachIdSignal.update(id => id);
  }

  refreshCoachReviews(): void {
    this.reviewsCoachIdSignal.update(id => id);
  }

  private getMockReviews(): CoachReview[] {
    return [
      {
        id: '1',
        userId: 'user1',
        userName: 'Alex K.',
        reviewerName: 'Alex K.',
        userAvatar: 'https://static.motiffcontent.com/private/resource/image/197d1db92b98e47-156a3a41-0682-4d96-b10a-c3f4022e94c2.jpeg',
        rating: 5,
        comment: 'Sarah helped me achieve my fitness goals in just 3 months. Her personalized approach made all the difference!',
        date: new Date('2024-01-15'),
        helpfulCount: 12,
        isEditable: false
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'Jessica T.',
        reviewerName: 'Jessica T.',
        userAvatar: 'https://static.motiffcontent.com/private/resource/image/197d1db92b9650e-3144cdc5-81f4-4516-9799-7f7e70751824.jpeg',
        rating: 5,
        comment: 'The best trainer I\'ve ever worked with. Knowledgeable, motivating and truly cares about her clients\' progress.',
        date: new Date('2024-01-01'),
        helpfulCount: 8,
        isEditable: false
      }
    ];
  }
}
