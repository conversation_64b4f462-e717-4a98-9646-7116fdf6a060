export interface Coach {
  id: string;
  name: string;
  title?: string;
  avatar: string;
  rating: number;
  reviewCount: number;
  bio?: string;
  motto?: string;
  experience?: number;
  isPro?: boolean;
  isFavorite?: boolean;
  location?: string;
  sessionCount?: number;
  pricePerSession?: number;

  // Target Goals
  targetGoals: TargetGoal[];

  // Specialties
  specialties: Specialty[];

  // Certifications
  certifications: Certification[];

  // Location/Environment
  environments: Environment[];

  // Special Considerations
  specialConsiderations: SpecialConsideration[];

  // Media
  videoUrl?: string;
  gallery?: string[];

  // Workouts
  workouts?: CoachWorkout[];

  // Reviews
  reviews?: CoachReview[];

  // Availability and pricing
  availability?: Availability;
  priceRange?: PriceRange;
  languages?: string[];
}

export interface TargetGoal {
  id: string;
  name: string;
  level?: number; // 1-5 scale
}

export interface Specialty {
  id: string;
  name: string;
  icon?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer?: string;
  organization: string;
  year?: number;
  verified?: boolean;
}

export interface Environment {
  id: string;
  name: string;
  icon?: string;
}

export interface SpecialConsideration {
  id: string;
  name: string;
  description?: string;
}

export interface CoachWorkout {
  id: string;
  title: string;
  description?: string;
  duration?: number;
  difficulty?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | 'Professional';
  thumbnail?: string;
  videoUrl?: string;
}

export interface CoachReview {
  id: string;
  userId: string;
  userName: string;
  reviewerName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  date: Date;
  helpfulCount?: number;
  isEditable?: boolean; // True if current user can edit this review
}

export interface Availability {
  days: string[];
  timeSlots: TimeSlot[];
}

export interface TimeSlot {
  start: string;
  end: string;
}

export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

// Filter interfaces
export interface CoachFilters {
  searchQuery?: string;
  targetGoals?: string[];
  environments?: string[];
  specialConsiderations?: string[];
  experienceLevel?: ExperienceLevel[];
  specialties?: string[];
  availability?: string[];
  rating?: RatingFilter;
  priceRange?: PriceRangeFilter;
  languages?: string[];
  showFavoritesOnly?: boolean;
}

export interface RatingFilter {
  min: number;
  max?: number;
}

export interface PriceRangeFilter {
  min: number;
  max: number;
}

export type ExperienceLevel = 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | 'Professional';

export type SortOption = 'rating-asc' | 'rating-desc' | 'name-asc' | 'name-desc' | 'experience-asc' | 'experience-desc';

// Constants for filter options
export const TARGET_GOALS = [
  { id: 'muscle-gain', name: 'Muscle Gain' },
  { id: 'weight-loss', name: 'Weight Loss' },
  { id: 'flexibility', name: 'Flexibility Improvement' },
  { id: 'rehabilitation', name: 'Rehabilitation' },
  { id: 'mobility', name: 'Mobility Enhancement' },
  { id: 'relaxation', name: 'Relaxation & Stress Relief' },
  { id: 'endurance', name: 'Endurance' },
  { id: 'posture', name: 'Posture Correction' }
];

export const ENVIRONMENTS = [
  { id: 'gym', name: 'Gym' },
  { id: 'home', name: 'Home' },
  { id: 'office', name: 'Office (desk stretching)' },
  { id: 'outdoor', name: 'Outdoor' },
  { id: 'limited-space', name: 'Limited Space' }
];

export const SPECIAL_CONSIDERATIONS = [
  { id: 'pregnancy-safe', name: 'Pregnancy-safe' },
  { id: 'senior-friendly', name: 'Senior-friendly' },
  { id: 'disability-adapted', name: 'Disability-adapted' },
  { id: 'injury-safe', name: 'Injury-safe (joint-friendly, back-safe)' },
  { id: 'desk-friendly', name: 'Desk-friendly (Office Stretches)' },
  { id: 'travel-friendly', name: 'Travel-friendly' }
];

export const EXPERIENCE_LEVELS: ExperienceLevel[] = [
  'Beginner',
  'Intermediate',
  'Advanced',
  'Expert',
  'Professional'
];
