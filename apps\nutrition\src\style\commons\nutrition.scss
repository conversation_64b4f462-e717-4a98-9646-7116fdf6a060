// =============================================================================
// NUTRITION THEME DESIGN SYSTEM
// Fresh and clean nutrition tracking interface emphasizing health and vitality
// =============================================================================

// -----------------------------------------------------------------------------
// CSS CUSTOM PROPERTIES (DESIGN TOKENS)
// -----------------------------------------------------------------------------

:root {
  // COLOR PALETTE
  // Primary Green Scale
  --nutrition-green-50: #E5F7ED;
  --nutrition-green-100: #CCF2DC;
  --nutrition-green-200: #99E6B8;
  --nutrition-green-300: #66D995;
  --nutrition-green-400: #33CD71;
  --nutrition-green-500: #2ECC71;
  --nutrition-green-600: #27AE60;
  --nutrition-green-700: #229954;
  --nutrition-green-800: #1E8449;
  --nutrition-green-900: #196F3D;

  // Secondary Blue Scale
  --nutrition-blue-light: #87CEEB;
  --nutrition-blue-medium: #4AC6E3;
  --nutrition-blue-primary: #4AC6E3;

  // Neutral Colors
  --nutrition-white: #FFFFFF;
  --nutrition-background: #F9FAFB;
  --nutrition-gray-50: #F5F5F5;
  --nutrition-gray-100: #E5E7EB;
  --nutrition-gray-400: #9CA3AF;
  --nutrition-gray-500: #6B7280;
  --nutrition-gray-800: #333333;
  --nutrition-gray-900: #000000;

  // Semantic Colors
  --nutrition-success: #2ECC71;
  --nutrition-info: #4AC6E3;
  --nutrition-warning: #FFE135;
  --nutrition-accent: #FFD700;

  // Gradients
  --nutrition-gradient-header: linear-gradient(90deg, #2ECC71 0%, #27AE60 100%);
  --nutrition-gradient-summary: linear-gradient(90deg, #4AC6E3 0%, #87CEEB 100%);
  --nutrition-gradient-progress: linear-gradient(90deg, #4AC6E3 0%, #87CEEB 100%);

  // TYPOGRAPHY
  --nutrition-font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  
  // Font Sizes
  --nutrition-text-xs: 12px;
  --nutrition-text-sm: 14px;
  --nutrition-text-base: 16px;
  --nutrition-text-lg: 18px;
  --nutrition-text-xl: 20px;
  --nutrition-text-2xl: 24px;
  --nutrition-text-3xl: 32px;

  // Font Weights
  --nutrition-font-normal: 400;
  --nutrition-font-medium: 500;
  --nutrition-font-semibold: 600;
  --nutrition-font-bold: 700;

  // Line Heights
  --nutrition-leading-tight: 16px;
  --nutrition-leading-normal: 20px;
  --nutrition-leading-relaxed: 28px;
  --nutrition-leading-loose: 32px;

  // SPACING
  --nutrition-space-xs: 4px;
  --nutrition-space-sm: 8px;
  --nutrition-space-md: 12px;
  --nutrition-space-lg: 16px;
  --nutrition-space-xl: 20px;
  --nutrition-space-2xl: 24px;
  --nutrition-space-3xl: 40px;
  --nutrition-space-4xl: 54px;
  --nutrition-space-5xl: 96px;

  // BORDER RADIUS
  --nutrition-rounded-none: 0px;
  --nutrition-rounded-sm: 4px;
  --nutrition-rounded-md: 8px;
  --nutrition-rounded-lg: 12px;
  --nutrition-rounded-xl: 16px;
  --nutrition-rounded-full: 9999px;

  // SHADOWS
  --nutrition-shadow-card: 0px 2px 4px -2px rgba(0, 0, 0, 0.10), 0px 4px 6px -1px rgba(0, 0, 0, 0.10);
  --nutrition-shadow-detailed: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 4px 4px 0px rgba(0, 0, 0, 0.20);

  // COMPONENT SIZES
  --nutrition-mobile-width: 390px;
  --nutrition-icon-sm: 16px;
  --nutrition-icon-md: 20px;
  --nutrition-icon-lg: 24px;
  --nutrition-icon-xl: 32px;
  --nutrition-food-image-size: 100px;
  --nutrition-avatar-size: 80px;
}

// -----------------------------------------------------------------------------
// BASE STYLES
// -----------------------------------------------------------------------------

.nutrition-theme {
  font-family: var(--nutrition-font-family);
  background-color: var(--nutrition-background);
  color: var(--nutrition-gray-800);
  font-size: var(--nutrition-text-sm);
  font-weight: var(--nutrition-font-normal);
  line-height: var(--nutrition-leading-normal);
}

// -----------------------------------------------------------------------------
// TYPOGRAPHY UTILITIES
// -----------------------------------------------------------------------------

.nutrition-text-app-title {
  font-size: var(--nutrition-text-2xl);
  font-weight: var(--nutrition-font-semibold);
  line-height: var(--nutrition-leading-loose);
  color: var(--nutrition-white);
}

.nutrition-text-section-title {
  font-size: var(--nutrition-text-xl);
  font-weight: var(--nutrition-font-semibold);
  line-height: var(--nutrition-leading-relaxed);
  color: var(--nutrition-gray-800);
}

.nutrition-text-card-title {
  font-size: var(--nutrition-text-lg);
  font-weight: var(--nutrition-font-medium);
  line-height: var(--nutrition-leading-relaxed);
  color: var(--nutrition-gray-800);
}

.nutrition-text-body {
  font-size: var(--nutrition-text-sm);
  font-weight: var(--nutrition-font-normal);
  line-height: var(--nutrition-leading-normal);
  color: var(--nutrition-gray-800);
}

.nutrition-text-caption {
  font-size: var(--nutrition-text-xs);
  font-weight: var(--nutrition-font-normal);
  line-height: var(--nutrition-leading-tight);
  color: var(--nutrition-gray-500);
}

.nutrition-text-calorie {
  font-size: var(--nutrition-text-sm);
  font-weight: var(--nutrition-font-medium);
  line-height: var(--nutrition-leading-normal);
  color: var(--nutrition-gray-800);
}

.nutrition-text-premium {
  font-size: var(--nutrition-text-xs);
  font-weight: var(--nutrition-font-medium);
  line-height: var(--nutrition-leading-tight);
  color: var(--nutrition-green-500);
}

// -----------------------------------------------------------------------------
// LAYOUT COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-container {
  width: 100%;
  max-width: var(--nutrition-mobile-width);
  margin: 0 auto;
  background-color: var(--nutrition-background);
}

.nutrition-section {
  margin-bottom: var(--nutrition-space-2xl);

  &:last-child {
    margin-bottom: 0;
  }
}

.nutrition-content-padding {
  padding-left: var(--nutrition-space-xl);
  padding-right: var(--nutrition-space-xl);
}

// -----------------------------------------------------------------------------
// HEADER COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-status-bar {
  background-color: var(--nutrition-white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--nutrition-space-md) var(--nutrition-space-xl);

  &__time {
    @extend .nutrition-text-body;
    color: var(--nutrition-gray-900);
  }

  &__indicators {
    display: flex;
    gap: var(--nutrition-space-sm);
    align-items: center;
    padding-top: 2px;
    padding-bottom: 2px;

    .nutrition-icon {
      width: var(--nutrition-icon-sm);
      height: var(--nutrition-icon-sm);
    }
  }
}

.nutrition-header {
  background: var(--nutrition-gradient-header);
  display: flex;
  align-items: center;
  padding: var(--nutrition-space-lg) var(--nutrition-space-xl);

  &__title {
    @extend .nutrition-text-app-title;
  }
}

// -----------------------------------------------------------------------------
// CARD COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-card {
  background-color: var(--nutrition-white);
  border-radius: var(--nutrition-rounded-lg);
  box-shadow: var(--nutrition-shadow-card);
  overflow: hidden;

  &--detailed {
    box-shadow: var(--nutrition-shadow-detailed);
  }

  &--gradient {
    background: var(--nutrition-gradient-summary);
    color: var(--nutrition-white);
  }

  &--progress {
    background: var(--nutrition-gradient-progress);
    color: var(--nutrition-white);
  }
}

.nutrition-card-padding {
  padding: var(--nutrition-space-lg);
}

.nutrition-card-content {
  display: flex;
  flex-direction: column;
  gap: var(--nutrition-space-sm);
}

// -----------------------------------------------------------------------------
// SUMMARY CARD
// -----------------------------------------------------------------------------

.nutrition-summary-card {
  @extend .nutrition-card;
  @extend .nutrition-card--gradient;
  @extend .nutrition-card-padding;
  margin: var(--nutrition-space-xl);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--nutrition-space-lg);
  }

  &__title-section {
    display: flex;
    flex-direction: column;
    gap: var(--nutrition-space-xs);
    padding-top: var(--nutrition-space-lg);
    padding-bottom: var(--nutrition-space-lg);

    .nutrition-text-card-title {
      color: var(--nutrition-white);
    }

    .nutrition-text-body {
      color: var(--nutrition-white);
    }
  }

  &__progress-circle {
    width: var(--nutrition-avatar-size);
    height: var(--nutrition-avatar-size);
    border-radius: var(--nutrition-rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .nutrition-text-caption {
      color: var(--nutrition-white);
    }

    .nutrition-text-body {
      color: var(--nutrition-white);
      font-weight: var(--nutrition-font-semibold);
    }
  }

  &__stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--nutrition-space-sm);

    .nutrition-text-caption {
      color: var(--nutrition-white);
    }
  }

  &__values {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nutrition-text-lg {
      color: var(--nutrition-white);
      font-weight: var(--nutrition-font-semibold);
      line-height: var(--nutrition-leading-relaxed);
    }
  }
}

// -----------------------------------------------------------------------------
// MEAL CARD
// -----------------------------------------------------------------------------

.nutrition-meal-card {
  @extend .nutrition-card;
  @extend .nutrition-card-padding;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--nutrition-space-lg);
  }

  &__title-section {
    display: flex;
    flex-direction: column;
    gap: var(--nutrition-space-xs);

    .nutrition-text-card-title {
      color: var(--nutrition-gray-800);
    }

    .nutrition-text-body {
      color: var(--nutrition-gray-500);
    }
  }

  &__add-button {
    @extend .nutrition-btn-add;
    width: var(--nutrition-icon-xl);
    height: var(--nutrition-icon-xl);
    margin-top: var(--nutrition-space-sm);
    margin-bottom: var(--nutrition-space-sm);
  }

  &__food-list {
    display: flex;
    flex-direction: column;
    gap: var(--nutrition-space-lg);
  }
}

// -----------------------------------------------------------------------------
// FOOD ITEM
// -----------------------------------------------------------------------------

.nutrition-food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--nutrition-space-md);

  &__content {
    display: flex;
    align-items: center;
    gap: var(--nutrition-space-md);
    flex: 1;
  }

  &__icon {
    background-color: var(--nutrition-gray-50);
    border-radius: var(--nutrition-rounded-full);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .nutrition-icon {
      width: var(--nutrition-icon-lg);
      height: var(--nutrition-icon-lg);
      border-radius: var(--nutrition-rounded-full);
    }
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-top: 2px;

    .nutrition-text-body {
      font-weight: var(--nutrition-font-medium);
    }

    .nutrition-text-caption {
      color: var(--nutrition-gray-500);
    }
  }

  &__calories {
    @extend .nutrition-text-calorie;
    margin-top: 10px;
    flex-shrink: 0;
  }
}

// -----------------------------------------------------------------------------
// BUTTON COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-btn {
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: inherit;
  font-weight: var(--nutrition-font-medium);
  text-decoration: none;

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:active {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &:focus-visible {
    outline: 2px solid var(--nutrition-green-500);
    outline-offset: 2px;
  }
}

.nutrition-btn-add {
  @extend .nutrition-btn;
  background-color: var(--nutrition-green-500);
  color: var(--nutrition-white);
  border-radius: var(--nutrition-rounded-full);
  padding: 6px;

  &--small {
    width: var(--nutrition-icon-lg);
    height: var(--nutrition-icon-lg);
    padding: 4px;
  }

  &--medium {
    width: var(--nutrition-icon-xl);
    height: var(--nutrition-icon-xl);
    padding: 6px;
  }

  &--large {
    width: 48px;
    height: 48px;
    padding: 12px;
  }

  .nutrition-icon {
    width: var(--nutrition-icon-md);
    height: var(--nutrition-icon-md);
  }
}

// -----------------------------------------------------------------------------
// QUICK ADD GRID
// -----------------------------------------------------------------------------

.nutrition-quick-add {
  &__grid {
    display: flex;
    gap: var(--nutrition-space-lg);
    overflow-x: auto;
    padding: 0 var(--nutrition-space-xl);
    scroll-snap-type: x mandatory;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--nutrition-space-sm);
    flex-shrink: 0;
    scroll-snap-align: start;
  }

  &__image {
    width: var(--nutrition-food-image-size);
    height: var(--nutrition-food-image-size);
    border-radius: var(--nutrition-rounded-lg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__label {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 2px;

    .nutrition-text-body {
      font-weight: var(--nutrition-font-medium);
    }

    .nutrition-text-caption {
      color: var(--nutrition-gray-500);
    }
  }
}

// -----------------------------------------------------------------------------
// PROFILE COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-profile-card {
  @extend .nutrition-card;
  @extend .nutrition-card-padding;
  display: flex;
  align-items: center;
  gap: var(--nutrition-space-md);
  margin: var(--nutrition-space-xl);

  &__avatar {
    background-color: var(--nutrition-green-50);
    border-radius: var(--nutrition-rounded-full);
    width: var(--nutrition-avatar-size);
    height: var(--nutrition-avatar-size);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .nutrition-text-2xl {
      color: var(--nutrition-green-500);
      font-weight: var(--nutrition-font-semibold);
      line-height: var(--nutrition-leading-loose);
    }
  }
}

.nutrition-profile-header {
  display: flex;
  align-items: center;
  gap: var(--nutrition-space-4xl);
  margin-bottom: var(--nutrition-space-lg);
  justify-content: center;

  &__name {
    @extend .nutrition-text-xl !optional;
    font-weight: var(--nutrition-font-semibold);
    text-align: center;
  }

  &__badge {
    background-color: var(--nutrition-green-50);
    color: var(--nutrition-green-500);
    border-radius: var(--nutrition-rounded-full);
    padding: 6px var(--nutrition-space-sm);
    font-size: var(--nutrition-text-xs);
    font-weight: var(--nutrition-font-medium);
    line-height: var(--nutrition-leading-tight);
  }
}

.nutrition-profile-email {
  display: flex;
  align-items: center;
  gap: var(--nutrition-space-md);
  justify-content: center;
  margin-bottom: var(--nutrition-space-3xl);

  &__icon {
    background-color: var(--nutrition-green-500);
    border-radius: var(--nutrition-rounded-full);
    width: var(--nutrition-icon-lg);
    height: var(--nutrition-icon-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;

    .nutrition-icon {
      width: var(--nutrition-icon-sm);
      height: var(--nutrition-icon-sm);
      color: var(--nutrition-white);
    }
  }

  &__text {
    @extend .nutrition-text-body;
    color: var(--nutrition-gray-500);
  }
}

// -----------------------------------------------------------------------------
// STATS GRID
// -----------------------------------------------------------------------------

.nutrition-stats-grid {
  @extend .nutrition-card;
  @extend .nutrition-card-padding;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--nutrition-space-lg);
  }

  &__icons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--nutrition-space-sm);
    padding: 0 21px;

    .nutrition-icon-circle {
      background-color: var(--nutrition-green-50);
      border-radius: var(--nutrition-rounded-full);
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;

      .nutrition-icon {
        width: var(--nutrition-icon-lg);
        height: var(--nutrition-icon-lg);
        color: var(--nutrition-green-500);
      }
    }
  }

  &__labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--nutrition-space-sm);

    .nutrition-text-caption {
      text-align: center;
      flex: 1;
    }
  }

  &__values {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 21px;

    .nutrition-text-body {
      text-align: center;
      flex: 1;
    }
  }
}

// -----------------------------------------------------------------------------
// PROGRESS CARD
// -----------------------------------------------------------------------------

.nutrition-progress-card {
  @extend .nutrition-card;
  @extend .nutrition-card--progress;
  @extend .nutrition-card-padding;

  &__content {
    display: flex;
    align-items: center;
    gap: var(--nutrition-space-sm);
  }

  &__text {
    flex: 1;
    padding-top: 10px;
    padding-bottom: 10px;

    .nutrition-text-body {
      color: var(--nutrition-white);
    }

    .nutrition-text-caption {
      color: var(--nutrition-white);
    }
  }

  &__circles {
    display: flex;
    gap: var(--nutrition-space-sm);
    align-items: center;
  }

  &__circle {
    width: 56px;
    height: 56px;
    border-radius: var(--nutrition-rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    .nutrition-text-caption {
      color: var(--nutrition-white);
    }
  }
}

// -----------------------------------------------------------------------------
// SETTINGS COMPONENTS
// -----------------------------------------------------------------------------

.nutrition-settings-card {
  @extend .nutrition-card;
  @extend .nutrition-card-padding;

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--nutrition-space-lg);
  }
}

.nutrition-settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  &__content {
    display: flex;
    align-items: center;
    gap: var(--nutrition-space-md);
    flex: 1;
  }

  &__icon {
    background-color: var(--nutrition-green-50);
    border-radius: var(--nutrition-rounded-full);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .nutrition-icon {
      width: var(--nutrition-icon-md);
      height: var(--nutrition-icon-md);
      color: var(--nutrition-green-500);
    }
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-top: 2px;

    .nutrition-text-body {
      color: var(--nutrition-gray-800);
    }

    .nutrition-text-caption {
      color: var(--nutrition-gray-500);
    }
  }

  &__arrow {
    .nutrition-icon {
      width: var(--nutrition-icon-md);
      height: var(--nutrition-icon-md);
      color: var(--nutrition-gray-400);
    }
  }
}

// -----------------------------------------------------------------------------
// NAVIGATION
// -----------------------------------------------------------------------------

.nutrition-navigation {
  background-color: var(--nutrition-white);
  border-top: 1px solid var(--nutrition-gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10.5px var(--nutrition-space-xl);
  gap: var(--nutrition-space-3xl);

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--nutrition-space-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;

    &:hover {
      opacity: 0.8;
    }

    &--active {
      .nutrition-icon {
        color: var(--nutrition-green-500);
      }

      .nutrition-text-caption {
        color: var(--nutrition-green-500);
      }
    }

    &--inactive {
      .nutrition-icon {
        color: var(--nutrition-gray-400);
      }

      .nutrition-text-caption {
        color: var(--nutrition-gray-400);
      }
    }
  }

  &__icon {
    width: var(--nutrition-icon-lg);
    height: var(--nutrition-icon-lg);
  }

  &__label {
    @extend .nutrition-text-caption;
    text-align: center;
  }
}

.nutrition-home-indicator {
  background-color: var(--nutrition-white);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 14.5px var(--nutrition-space-5xl);

  &__bar {
    background-color: var(--nutrition-gray-900);
    border-radius: var(--nutrition-rounded-full);
    width: 134px;
    height: 5px;
    flex-grow: 1;
  }
}

// -----------------------------------------------------------------------------
// UTILITY CLASSES
// -----------------------------------------------------------------------------

// Spacing utilities
.nutrition-m-0 { margin: 0; }
.nutrition-m-xs { margin: var(--nutrition-space-xs); }
.nutrition-m-sm { margin: var(--nutrition-space-sm); }
.nutrition-m-md { margin: var(--nutrition-space-md); }
.nutrition-m-lg { margin: var(--nutrition-space-lg); }
.nutrition-m-xl { margin: var(--nutrition-space-xl); }
.nutrition-m-2xl { margin: var(--nutrition-space-2xl); }

.nutrition-mt-0 { margin-top: 0; }
.nutrition-mt-xs { margin-top: var(--nutrition-space-xs); }
.nutrition-mt-sm { margin-top: var(--nutrition-space-sm); }
.nutrition-mt-md { margin-top: var(--nutrition-space-md); }
.nutrition-mt-lg { margin-top: var(--nutrition-space-lg); }
.nutrition-mt-xl { margin-top: var(--nutrition-space-xl); }
.nutrition-mt-2xl { margin-top: var(--nutrition-space-2xl); }
.nutrition-mt-3xl { margin-top: var(--nutrition-space-3xl); }

.nutrition-mb-0 { margin-bottom: 0; }
.nutrition-mb-xs { margin-bottom: var(--nutrition-space-xs); }
.nutrition-mb-sm { margin-bottom: var(--nutrition-space-sm); }
.nutrition-mb-md { margin-bottom: var(--nutrition-space-md); }
.nutrition-mb-lg { margin-bottom: var(--nutrition-space-lg); }
.nutrition-mb-xl { margin-bottom: var(--nutrition-space-xl); }
.nutrition-mb-2xl { margin-bottom: var(--nutrition-space-2xl); }

.nutrition-p-0 { padding: 0; }
.nutrition-p-xs { padding: var(--nutrition-space-xs); }
.nutrition-p-sm { padding: var(--nutrition-space-sm); }
.nutrition-p-md { padding: var(--nutrition-space-md); }
.nutrition-p-lg { padding: var(--nutrition-space-lg); }
.nutrition-p-xl { padding: var(--nutrition-space-xl); }
.nutrition-p-2xl { padding: var(--nutrition-space-2xl); }

// Display utilities
.nutrition-flex { display: flex; }
.nutrition-flex-col { flex-direction: column; }
.nutrition-flex-row { flex-direction: row; }
.nutrition-items-center { align-items: center; }
.nutrition-items-start { align-items: flex-start; }
.nutrition-items-end { align-items: flex-end; }
.nutrition-justify-center { justify-content: center; }
.nutrition-justify-between { justify-content: space-between; }
.nutrition-justify-start { justify-content: flex-start; }
.nutrition-justify-end { justify-content: flex-end; }

// Gap utilities
.nutrition-gap-xs { gap: var(--nutrition-space-xs); }
.nutrition-gap-sm { gap: var(--nutrition-space-sm); }
.nutrition-gap-md { gap: var(--nutrition-space-md); }
.nutrition-gap-lg { gap: var(--nutrition-space-lg); }
.nutrition-gap-xl { gap: var(--nutrition-space-xl); }
.nutrition-gap-2xl { gap: var(--nutrition-space-2xl); }

// Color utilities
.nutrition-text-white { color: var(--nutrition-white); }
.nutrition-text-gray-500 { color: var(--nutrition-gray-500); }
.nutrition-text-gray-800 { color: var(--nutrition-gray-800); }
.nutrition-text-green-500 { color: var(--nutrition-green-500); }

.nutrition-bg-white { background-color: var(--nutrition-white); }
.nutrition-bg-green-50 { background-color: var(--nutrition-green-50); }
.nutrition-bg-green-500 { background-color: var(--nutrition-green-500); }
.nutrition-bg-gray-50 { background-color: var(--nutrition-gray-50); }

// Border radius utilities
.nutrition-rounded-none { border-radius: var(--nutrition-rounded-none); }
.nutrition-rounded-sm { border-radius: var(--nutrition-rounded-sm); }
.nutrition-rounded-md { border-radius: var(--nutrition-rounded-md); }
.nutrition-rounded-lg { border-radius: var(--nutrition-rounded-lg); }
.nutrition-rounded-xl { border-radius: var(--nutrition-rounded-xl); }
.nutrition-rounded-full { border-radius: var(--nutrition-rounded-full); }

// Shadow utilities
.nutrition-shadow-card { box-shadow: var(--nutrition-shadow-card); }
.nutrition-shadow-detailed { box-shadow: var(--nutrition-shadow-detailed); }

// Width and height utilities
.nutrition-w-full { width: 100%; }
.nutrition-h-full { height: 100%; }
.nutrition-w-auto { width: auto; }
.nutrition-h-auto { height: auto; }

// -----------------------------------------------------------------------------
// IONIC COMPONENT OVERRIDES
// -----------------------------------------------------------------------------

// Override Ionic components for nutrition theme
.nutrition-theme {
  // Card overrides
  ion-card {
    background: var(--nutrition-white);
    border-radius: var(--nutrition-rounded-lg);
    box-shadow: var(--nutrition-shadow-card);
    margin: var(--nutrition-space-lg);

    &.nutrition-card-gradient {
      background: var(--nutrition-gradient-summary);
      color: var(--nutrition-white);
    }
  }

  ion-card-header {
    padding: var(--nutrition-space-lg) var(--nutrition-space-lg) 0;
  }

  ion-card-content {
    padding: var(--nutrition-space-lg);
  }

  // Button overrides
  ion-button {
    --border-radius: var(--nutrition-rounded-lg);
    --font-family: var(--nutrition-font-family);
    --font-weight: var(--nutrition-font-medium);

    &.nutrition-btn-primary {
      --background: var(--nutrition-green-500);
      --background-hover: var(--nutrition-green-600);
      --color: var(--nutrition-white);
    }

    &.nutrition-btn-add {
      --border-radius: var(--nutrition-rounded-full);
      --background: var(--nutrition-green-500);
      --color: var(--nutrition-white);
      width: var(--nutrition-icon-xl);
      height: var(--nutrition-icon-xl);
      margin: 0;
    }
  }

  // Header overrides
  ion-header ion-toolbar {
    --background: var(--nutrition-gradient-header);
    --color: var(--nutrition-white);
    --padding-start: var(--nutrition-space-xl);
    --padding-end: var(--nutrition-space-xl);
    --padding-top: var(--nutrition-space-lg);
    --padding-bottom: var(--nutrition-space-lg);
  }

  ion-title {
    font-size: var(--nutrition-text-2xl);
    font-weight: var(--nutrition-font-semibold);
    font-family: var(--nutrition-font-family);
  }

  // Tab bar overrides
  ion-tab-bar {
    --background: var(--nutrition-white);
    --border: 1px solid var(--nutrition-gray-100);
    height: auto;
    padding: 10.5px var(--nutrition-space-xl);
  }

  ion-tab-button {
    --color: var(--nutrition-gray-400);
    --color-selected: var(--nutrition-green-500);
    font-family: var(--nutrition-font-family);
    font-size: var(--nutrition-text-xs);
    gap: var(--nutrition-space-xs);

    ion-icon {
      font-size: var(--nutrition-icon-lg);
    }
  }

  // Content overrides
  ion-content {
    --background: var(--nutrition-background);
    font-family: var(--nutrition-font-family);
  }

  // Avatar overrides
  ion-avatar {
    width: var(--nutrition-avatar-size);
    height: var(--nutrition-avatar-size);
    background: var(--nutrition-green-50);
    color: var(--nutrition-green-500);
    border-radius: var(--nutrition-rounded-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--nutrition-text-2xl);
    font-weight: var(--nutrition-font-semibold);
  }

  // Badge overrides
  ion-badge {
    --background: var(--nutrition-green-50);
    --color: var(--nutrition-green-500);
    --padding-start: var(--nutrition-space-sm);
    --padding-end: var(--nutrition-space-sm);
    --padding-top: 6px;
    --padding-bottom: 6px;
    border-radius: var(--nutrition-rounded-full);
    font-family: var(--nutrition-font-family);
    font-size: var(--nutrition-text-xs);
    font-weight: var(--nutrition-font-medium);
  }

  // List overrides
  ion-list {
    background: transparent;
    padding: 0;
  }

  ion-item {
    --background: var(--nutrition-white);
    --border-radius: var(--nutrition-rounded-lg);
    --inner-padding-end: var(--nutrition-space-lg);
    --inner-padding-start: var(--nutrition-space-lg);
    --inner-padding-top: var(--nutrition-space-lg);
    --inner-padding-bottom: var(--nutrition-space-lg);
    --min-height: auto;
    margin-bottom: var(--nutrition-space-lg);
    font-family: var(--nutrition-font-family);
  }

  // Icon overrides
  ion-icon {
    color: inherit;
    
    &.nutrition-icon-green {
      color: var(--nutrition-green-500);
    }

    &.nutrition-icon-gray {
      color: var(--nutrition-gray-400);
    }
  }
}

// -----------------------------------------------------------------------------
// ANIMATIONS
// -----------------------------------------------------------------------------

@keyframes nutrition-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes nutrition-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes nutrition-slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.nutrition-animate-fade-in {
  animation: nutrition-fade-in 0.3s ease-out;
}

.nutrition-animate-scale-in {
  animation: nutrition-scale-in 0.2s ease-out;
}

.nutrition-animate-slide-up {
  animation: nutrition-slide-up 0.4s ease-out;
}

// -----------------------------------------------------------------------------
// RESPONSIVE DESIGN
// -----------------------------------------------------------------------------

@media (max-width: 359px) {
  .nutrition-container {
    padding: 0 var(--nutrition-space-md);
  }

  .nutrition-quick-add__grid {
    padding: 0 var(--nutrition-space-md);
    gap: var(--nutrition-space-md);
  }

  .nutrition-food-image-size {
    --nutrition-food-image-size: 80px;
  }
}

@media (min-width: 768px) {
  .nutrition-container {
    max-width: 600px;
  }

  .nutrition-quick-add__grid {
    justify-content: center;
    flex-wrap: wrap;
    overflow-x: visible;
  }
}

@media (min-width: 1024px) {
  .nutrition-container {
    max-width: 800px;
  }

  .nutrition-section {
    margin-bottom: var(--nutrition-space-3xl);
  }
}

// -----------------------------------------------------------------------------
// ACCESSIBILITY
// -----------------------------------------------------------------------------

// High contrast mode support
@media (prefers-contrast: high) {
  :root {
    --nutrition-gray-400: #666666;
    --nutrition-gray-500: #555555;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Focus indicators
.nutrition-theme {
  *:focus-visible {
    outline: 2px solid var(--nutrition-green-500);
    outline-offset: 2px;
    border-radius: var(--nutrition-rounded-sm);
  }
}

// Screen reader only content
.nutrition-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// -----------------------------------------------------------------------------
// USAGE EXAMPLES
// -----------------------------------------------------------------------------

/*
Example HTML Structure:

<div class="nutrition-theme">
  <!-- Header -->
  <div class="nutrition-status-bar">
    <div class="nutrition-status-bar__time">9:41</div>
    <div class="nutrition-status-bar__indicators">
      <ion-icon name="signal"></ion-icon>
      <ion-icon name="wifi"></ion-icon>
      <ion-icon name="battery-full"></ion-icon>
    </div>
  </div>

  <div class="nutrition-header">
    <h1 class="nutrition-header__title">NutriTrack</h1>
  </div>

  <!-- Summary Card -->
  <div class="nutrition-summary-card">
    <div class="nutrition-summary-card__header">
      <div class="nutrition-summary-card__title-section">
        <h2 class="nutrition-text-card-title">Daily Summary</h2>
        <p class="nutrition-text-body">Monday, June 10</p>
      </div>
      <div class="nutrition-summary-card__progress-circle">
        <span class="nutrition-text-caption">Calories</span>
        <span class="nutrition-text-body">450</span>
      </div>
    </div>
    <!-- Add stats and values here -->
  </div>

  <!-- Meal Cards -->
  <div class="nutrition-section nutrition-content-padding">
    <h2 class="nutrition-text-section-title">Today's Meals</h2>
    
    <div class="nutrition-meal-card nutrition-mt-lg">
      <div class="nutrition-meal-card__header">
        <div class="nutrition-meal-card__title-section">
          <h3 class="nutrition-text-card-title">Breakfast</h3>
          <p class="nutrition-text-body">7:30 AM</p>
        </div>
        <button class="nutrition-meal-card__add-button">
          <ion-icon name="add"></ion-icon>
        </button>
      </div>
      
      <div class="nutrition-meal-card__food-list">
        <div class="nutrition-food-item">
          <div class="nutrition-food-item__content">
            <div class="nutrition-food-item__icon">
              <div class="nutrition-icon" style="background: #FFD700;"></div>
            </div>
            <div class="nutrition-food-item__details">
              <span class="nutrition-text-body">Oatmeal</span>
              <span class="nutrition-text-caption">1 bowl (250g)</span>
            </div>
          </div>
          <span class="nutrition-food-item__calories">320 kcal</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <div class="nutrition-navigation">
    <div class="nutrition-navigation__item nutrition-navigation__item--active">
      <ion-icon name="home" class="nutrition-navigation__icon"></ion-icon>
      <span class="nutrition-navigation__label">Home</span>
    </div>
    <div class="nutrition-navigation__item nutrition-navigation__item--inactive">
      <ion-icon name="book" class="nutrition-navigation__icon"></ion-icon>
      <span class="nutrition-navigation__label">Diary</span>
    </div>
    <!-- More nav items -->
  </div>
</div>
*/ 