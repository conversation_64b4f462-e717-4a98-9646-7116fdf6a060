# Hibernate Persistence Utils Demo

This demo showcases the integration of **Hypersistence Utils** (formerly hibernate-types) with **Spring Boot** and **PostgreSQL**, demonstrating how to work with JSON and array data types in a relational database.

## Features Demonstrated

### Backend Implementation
- **Entity with JSON and Array attributes**: `Person` entity with JSONB and TEXT[] columns
- **Advanced Queries**: Native SQL queries for filtering by JSON properties and array elements
- **Repository Layer**: Comprehensive repository with search and statistics methods
- **REST API**: Complete CRUD operations and filtering endpoints
- **Data Initialization**: Sample data seeding on application startup
- **Testing**: Unit and integration tests

### Frontend Implementation
- **Angular Signals**: Reactive state management using <PERSON><PERSON>'s signal-based approach
- **Ionic UI Components**: Modern, responsive interface with Ionic Framework
- **HTTP Communication**: RESTful API integration with error handling
- **Multiple Views**: List, Search, Create, and Statistics sections
- **Real-time Feedback**: Loading states, success/error messages

## Hibernate Types Used

### JsonType (`@Type(JsonType.class)`)
```java
@Type(JsonType.class)
@Column(columnDefinition = "jsonb")
private Description description;
```
- Stores complex JSON objects in PostgreSQL JSONB columns
- Enables efficient querying of JSON properties
- Supports nested object structures

### ListArrayType (`@Type(ListArrayType.class)`)
```java
@Type(ListArrayType.class)
@Column(name = "skills", columnDefinition = "text[]")
private List<String> skills;
```
- Maps Java collections to PostgreSQL array columns
- Supports array operations and queries
- Efficient storage and retrieval of list data

## API Endpoints

### CRUD Operations
- `GET /api/hibernate-demo/persons` - Get all persons
- `POST /api/hibernate-demo/persons` - Create new person
- `GET /api/hibernate-demo/persons/{id}` - Get person by ID
- `PUT /api/hibernate-demo/persons/{id}` - Update person
- `DELETE /api/hibernate-demo/persons/{id}` - Delete person

### JSON-based Filtering
- `GET /api/hibernate-demo/persons/by-hobby/{hobby}` - Filter by hobby
- `GET /api/hibernate-demo/persons/by-favorite-color/{color}` - Filter by favorite color
- `GET /api/hibernate-demo/persons/by-age-range?minAge={min}&maxAge={max}` - Filter by age range

### Array-based Filtering
- `GET /api/hibernate-demo/persons/by-skill/{skill}` - Filter by specific skill
- `GET /api/hibernate-demo/persons/by-language/{language}` - Filter by language
- `GET /api/hibernate-demo/persons/by-any-skill?skills={skill1,skill2}` - Filter by any of the specified skills

### Statistics
- `GET /api/hibernate-demo/persons/count` - Total count
- `GET /api/hibernate-demo/persons/unique-skills` - All unique skills
- `GET /api/hibernate-demo/persons/statistics/hobbies` - Hobby distribution

## Technology Stack

- **Backend**: Java 22, Spring Boot 3.x, PostgreSQL, Hypersistence Utils
- **Frontend**: Angular 18+, Ionic 8+, TypeScript
- **Database**: PostgreSQL with JSONB and array support
- **Testing**: JUnit 5, Spring Boot Test, H2 (for tests)

## Key Learning Points

1. **Modern Hibernate Usage**: How to handle non-relational data in relational databases
2. **PostgreSQL Advanced Types**: Leveraging JSONB and array columns effectively
3. **Native Query Patterns**: Writing efficient SQL for JSON and array operations
4. **Angular Signals**: Implementing reactive UIs with the latest Angular features
5. **Full-Stack Integration**: Complete data flow from database to UI

## Running the Demo

1. Ensure PostgreSQL is running
2. Start the Spring Boot backend: `mvn spring-boot:run`
3. Start the Angular frontend: `npx nx serve nutrition`
4. Navigate to `/playground/hibernate` in your browser

The demo includes sample data that is automatically created on startup, so you can immediately explore all features without manual data entry. 