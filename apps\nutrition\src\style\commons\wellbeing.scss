// Wellbeing Design System
// Based on Motiff designs: Wellbeing Dashboard and Activities screens

.wellbeing {
  // ===== CSS CUSTOM PROPERTIES =====
  
  // Color Palette
  --wellbeing-accent: #E7A977;
  --wellbeing-accent-rgb: 231, 169, 119;
  
  // Backgrounds
  --wellbeing-bg-primary: #F5EFE6;
  --wellbeing-bg-secondary: #FFFFFF;
  --wellbeing-bg-tertiary: #D8C9B6;
  --wellbeing-bg-mood-overlay: rgba(216, 201, 182, 0.30);
  
  // Text Colors
  --wellbeing-text-primary: #5A4B3E;
  --wellbeing-text-secondary: #4B5563;
  --wellbeing-text-tertiary: #6B7280;
  --wellbeing-text-quaternary: #9CA3AF;
  --wellbeing-text-accent: #E7A977;
  --wellbeing-text-white: #FFFFFF;
  
  // Status Colors
  --wellbeing-success: #10B981;
  --wellbeing-warning: #F59E0B;
  --wellbeing-error: #EF4444;
  --wellbeing-info: #3B82F6;
  
  // Mood Colors
  --wellbeing-mood-selected: #E7A977;
  --wellbeing-mood-unselected: #D8C9B6;
  
  // Borders
  --wellbeing-border-light: #E5E7EB;
  --wellbeing-border-accent: #E7A977;
  
  // Overlays
  --wellbeing-overlay-light-05: rgba(0, 0, 0, 0.05);
  --wellbeing-overlay-light-10: rgba(0, 0, 0, 0.10);
  --wellbeing-overlay-white-30: rgba(255, 255, 255, 0.30);
  
  // Border & Radius
  --wellbeing-radius-sm: 4px;
  --wellbeing-radius-md: 8px;
  --wellbeing-radius-lg: 12px;
  --wellbeing-radius-xl: 16px;
  --wellbeing-radius-pill: 9999px;
  --wellbeing-radius-full: 50%;
  
  // Spacing
  --wellbeing-space-1: 4px;
  --wellbeing-space-2: 8px;
  --wellbeing-space-3: 12px;
  --wellbeing-space-4: 16px;
  --wellbeing-space-5: 20px;
  --wellbeing-space-6: 24px;
  --wellbeing-space-7: 28px;
  --wellbeing-space-8: 32px;
  --wellbeing-space-9: 36px;
  --wellbeing-space-10: 40px;
  --wellbeing-space-12: 48px;
  --wellbeing-space-16: 64px;
  --wellbeing-space-20: 80px;
  
  // Shadows
  --wellbeing-shadow-card: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --wellbeing-shadow-subtle: 0px 1px 3px 0px rgba(0, 0, 0, 0.1), 0px 1px 2px 0px rgba(0, 0, 0, 0.06);
  
  // Typography
  --wellbeing-font-family: 'Comfortaa', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --wellbeing-font-xs: 12px;
  --wellbeing-font-sm: 14px;
  --wellbeing-font-base: 16px;
  --wellbeing-font-lg: 18px;
  --wellbeing-font-xl: 20px;
  --wellbeing-font-2xl: 22px;
  --wellbeing-font-3xl: 24px;
  
  // Transitions
  --wellbeing-transition-fast: 0.15s ease-out;
  --wellbeing-transition-normal: 0.3s ease-out;
  --wellbeing-transition-slow: 0.5s ease-out;

  // ===== BASE STYLES =====
  
  font-family: var(--wellbeing-font-family);
  background: var(--wellbeing-bg-primary);
  color: var(--wellbeing-text-primary);
  
  // ===== TYPOGRAPHY CLASSES =====
  
  .wellbeing-display-large {
    font-size: var(--wellbeing-font-2xl);
    font-weight: 600;
    line-height: 33px;
    color: var(--wellbeing-text-primary);
  }
  
  .wellbeing-heading-large {
    font-size: var(--wellbeing-font-lg);
    font-weight: 600;
    line-height: 28px;
    color: var(--wellbeing-text-primary);
  }
  
  .wellbeing-heading-medium {
    font-size: var(--wellbeing-font-base);
    font-weight: 600;
    line-height: 24px;
    color: var(--wellbeing-text-primary);
  }
  
  .wellbeing-heading-small {
    font-size: var(--wellbeing-font-base);
    font-weight: 500;
    line-height: 24px;
    color: var(--wellbeing-text-primary);
  }
  
  .wellbeing-body-large {
    font-size: var(--wellbeing-font-base);
    font-weight: 400;
    line-height: 24px;
    color: var(--wellbeing-text-secondary);
  }
  
  .wellbeing-body-medium {
    font-size: var(--wellbeing-font-sm);
    font-weight: 400;
    line-height: 20px;
    color: var(--wellbeing-text-secondary);
  }
  
  .wellbeing-body-small {
    font-size: var(--wellbeing-font-xs);
    font-weight: 400;
    line-height: 16px;
    color: var(--wellbeing-text-tertiary);
  }
  
  .wellbeing-label-medium {
    font-size: var(--wellbeing-font-sm);
    font-weight: 500;
    line-height: 20px;
    color: var(--wellbeing-text-primary);
  }
  
  // ===== BUTTON STYLES =====
  
  .wellbeing-btn {
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--wellbeing-transition-normal);
    font-family: var(--wellbeing-font-family);
    
    &:focus {
      outline: 2px solid var(--wellbeing-accent);
      outline-offset: 2px;
    }
  }
  
  .wellbeing-btn-primary {
    @extend .wellbeing-btn;
    background: var(--wellbeing-accent);
    color: var(--wellbeing-text-white);
    border-radius: var(--wellbeing-radius-pill);
    padding: var(--wellbeing-space-1) var(--wellbeing-space-4);
    font-weight: 500;
    font-size: var(--wellbeing-font-sm);
    line-height: 20px;
    
    &:hover {
      transform: scale(1.02);
      opacity: 0.9;
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  .wellbeing-btn-secondary {
    @extend .wellbeing-btn;
    background: var(--wellbeing-bg-tertiary);
    color: var(--wellbeing-text-primary);
    border-radius: var(--wellbeing-radius-pill);
    padding: var(--wellbeing-space-1) var(--wellbeing-space-4);
    font-weight: 400;
    font-size: var(--wellbeing-font-xs);
    line-height: 16px;
    
    &:hover {
      background: var(--wellbeing-accent);
      color: var(--wellbeing-text-white);
    }
  }
  
  .wellbeing-btn-mood {
    @extend .wellbeing-btn;
    background: var(--wellbeing-mood-unselected);
    color: var(--wellbeing-text-white);
    border-radius: var(--wellbeing-radius-pill);
    padding: 14px;
    width: 56px;
    height: 56px;
    
    &.selected {
      background: var(--wellbeing-mood-selected);
    }
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .wellbeing-btn-play {
    @extend .wellbeing-btn;
    background: var(--wellbeing-accent);
    color: var(--wellbeing-text-white);
    border-radius: var(--wellbeing-radius-pill);
    padding: var(--wellbeing-space-4);
    width: 40px;
    height: 40px;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: var(--wellbeing-shadow-subtle);
    }
  }
  
  .wellbeing-btn-cta-text {
    @extend .wellbeing-btn;
    background: transparent;
    color: var(--wellbeing-accent);
    font-weight: 500;
    font-size: var(--wellbeing-font-sm);
    line-height: 20px;
    padding: var(--wellbeing-space-1) 0;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  // ===== CARD STYLES =====
  
  .wellbeing-card {
    background: var(--wellbeing-bg-secondary);
    border-radius: var(--wellbeing-radius-xl);
    padding: var(--wellbeing-space-4);
    box-shadow: var(--wellbeing-shadow-card);
    border: 1px solid var(--wellbeing-border-light);
    
    &.wellbeing-card-hover {
      transition: all var(--wellbeing-transition-normal);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--wellbeing-shadow-subtle);
      }
    }
  }
  
  .wellbeing-card-activity {
    @extend .wellbeing-card;
    overflow: hidden;
    padding: 0;
    display: flex;
    gap: var(--wellbeing-space-3);
    align-items: stretch;
    position: relative;
    
    .wellbeing-card-image {
      width: 90px;
      height: 90px;
      object-fit: cover;
      flex-shrink: 0;
    }
    
    .wellbeing-card-content {
      padding: var(--wellbeing-space-3);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
    }
    
    .wellbeing-card-play {
      position: absolute;
      right: var(--wellbeing-space-2);
      top: 50%;
      transform: translateY(-50%);
      background: var(--wellbeing-accent);
      color: var(--wellbeing-text-white);
      border: none;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-50%) scale(1.1);
      }
    }
  }
  
  .wellbeing-card-feature {
    background: var(--wellbeing-bg-mood-overlay);
    border-radius: var(--wellbeing-radius-xl);
    padding: var(--wellbeing-space-4);
    display: flex;
    flex-direction: column;
    gap: var(--wellbeing-space-2);
  }
  
  .wellbeing-card-mindfulness {
    background: transparent;
    border-radius: var(--wellbeing-radius-xl);
    padding: var(--wellbeing-space-5);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
      pointer-events: none;
    }
  }
  
  // ===== TAG STYLES =====
  
  .wellbeing-tag {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    font-family: var(--wellbeing-font-family);
    transition: all var(--wellbeing-transition-fast);
    
    &:focus {
      outline: 2px solid var(--wellbeing-accent);
      outline-offset: 2px;
    }
  }
  
  .wellbeing-tag-category {
    @extend .wellbeing-tag;
    background: var(--wellbeing-bg-tertiary);
    color: var(--wellbeing-text-primary);
    border-radius: var(--wellbeing-radius-pill);
    padding: var(--wellbeing-space-1) var(--wellbeing-space-4);
    font-size: var(--wellbeing-font-xs);
    line-height: 16px;
    
    &.selected {
      background: var(--wellbeing-accent);
      color: var(--wellbeing-text-white);
    }
    
    &:hover:not(.selected) {
      background: var(--wellbeing-accent);
      color: var(--wellbeing-text-white);
      opacity: 0.8;
    }
  }
  
  .wellbeing-tag-activity-type {
    @extend .wellbeing-tag;
    background: var(--wellbeing-bg-mood-overlay);
    color: var(--wellbeing-text-primary);
    border-radius: var(--wellbeing-radius-pill);
    padding: 2px var(--wellbeing-space-2);
    font-size: var(--wellbeing-font-xs);
    line-height: 16px;
  }
  
  .wellbeing-tag-time {
    @extend .wellbeing-tag;
    background: var(--wellbeing-accent);
    color: var(--wellbeing-text-white);
    border-radius: var(--wellbeing-radius-pill);
    padding: var(--wellbeing-space-1) var(--wellbeing-space-2);
    font-size: var(--wellbeing-font-xs);
    line-height: 16px;
  }
  
  // ===== NAVIGATION STYLES =====
  
  .wellbeing-nav-bottom {
    background: var(--wellbeing-bg-secondary);
    border-top: 1px solid var(--wellbeing-border-light);
    padding: 13px 16.55px 12px 16.14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--wellbeing-space-8);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }
  
  .wellbeing-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--wellbeing-space-1);
    color: var(--wellbeing-text-quaternary);
    text-decoration: none;
    font-size: var(--wellbeing-font-xs);
    font-weight: 400;
    transition: color var(--wellbeing-transition-fast);
    
    &.active {
      color: var(--wellbeing-accent);
    }
    
    &:hover {
      color: var(--wellbeing-text-primary);
    }
    
    ion-icon {
      font-size: 24px;
    }
  }
  
  // ===== SEARCH STYLES =====
  
  .wellbeing-search {
    background: var(--wellbeing-bg-secondary);
    border: 1px solid var(--wellbeing-border-light);
    border-radius: var(--wellbeing-radius-xl);
    padding: 14px 13px;
    display: flex;
    align-items: center;
    gap: var(--wellbeing-space-2);
    
    input {
      background: transparent;
      border: none;
      color: var(--wellbeing-text-primary);
      font-size: var(--wellbeing-font-sm);
      flex: 1;
      font-family: var(--wellbeing-font-family);
      
      &::placeholder {
        color: var(--wellbeing-text-quaternary);
      }
      
      &:focus {
        outline: none;
      }
    }
    
    ion-icon {
      color: var(--wellbeing-text-primary);
      font-size: 20px;
    }
  }
  
  // ===== MOOD SELECTOR =====
  
  .wellbeing-mood-selector {
    display: flex;
    flex-direction: column;
    gap: var(--wellbeing-space-2);
    
    .wellbeing-mood-buttons {
      display: flex;
      gap: var(--wellbeing-space-5);
      align-items: center;
    }
    
    .wellbeing-mood-labels {
      display: flex;
      gap: 25px;
      padding-left: 7px;
      font-size: var(--wellbeing-font-xs);
      line-height: 16px;
      color: var(--wellbeing-text-primary);
    }
  }
  
  // ===== LAYOUT UTILITIES =====
  
  .wellbeing-container {
    max-width: 390px;
    margin: 0 auto;
    padding: 0 var(--wellbeing-space-6);
  }
  
  .wellbeing-section {
    margin-bottom: var(--wellbeing-space-8);
  }
  
  .wellbeing-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--wellbeing-space-4);
  }
  
  .wellbeing-grid-category-tags {
    display: flex;
    gap: var(--wellbeing-space-3);
    overflow-x: auto;
    padding-bottom: var(--wellbeing-space-2);
    
    // Hide scrollbar
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  .wellbeing-space-y-4 > * + * {
    margin-top: var(--wellbeing-space-4);
  }
  
  .wellbeing-space-y-6 > * + * {
    margin-top: var(--wellbeing-space-6);
  }
  
  .wellbeing-space-y-8 > * + * {
    margin-top: var(--wellbeing-space-8);
  }
  
  // ===== BACKGROUND UTILITIES =====
  
  .wellbeing-bg-primary {
    background: var(--wellbeing-bg-primary);
  }
  
  .wellbeing-bg-secondary {
    background: var(--wellbeing-bg-secondary);
  }
  
  .wellbeing-bg-mood-overlay {
    background: var(--wellbeing-bg-mood-overlay);
  }
  
  // ===== TEXT UTILITIES =====
  
  .wellbeing-text-accent {
    color: var(--wellbeing-text-accent) !important;
  }
  
  .wellbeing-text-primary {
    color: var(--wellbeing-text-primary) !important;
  }
  
  .wellbeing-text-secondary {
    color: var(--wellbeing-text-secondary) !important;
  }
  
  .wellbeing-text-tertiary {
    color: var(--wellbeing-text-tertiary) !important;
  }
  
  .wellbeing-text-white {
    color: var(--wellbeing-text-white) !important;
  }
  
  // ===== GREETING SECTION =====
  
  .wellbeing-greeting {
    .wellbeing-greeting-title {
      font-size: var(--wellbeing-font-2xl);
      font-weight: 600;
      line-height: 33px;
      color: var(--wellbeing-text-primary);
      margin-bottom: var(--wellbeing-space-6);
    }
    
    .wellbeing-greeting-question {
      font-size: var(--wellbeing-font-base);
      line-height: 24px;
      color: var(--wellbeing-text-primary);
      margin-bottom: var(--wellbeing-space-4);
    }
  }
  
  // ===== FEATURE HIGHLIGHTS =====
  
  .wellbeing-feature-highlight {
    position: relative;
    border-radius: var(--wellbeing-radius-xl);
    overflow: hidden;
    padding: var(--wellbeing-space-5);
    color: var(--wellbeing-text-white);
    
    .wellbeing-feature-title {
      font-size: var(--wellbeing-font-xl);
      font-weight: 700;
      line-height: 28px;
      margin-bottom: var(--wellbeing-space-1);
    }
    
    .wellbeing-feature-subtitle {
      font-size: var(--wellbeing-font-sm);
      line-height: 20px;
    }
    
    .wellbeing-feature-action {
      position: absolute;
      bottom: var(--wellbeing-space-5);
      right: var(--wellbeing-space-5);
      background: var(--wellbeing-bg-secondary);
      border-radius: var(--wellbeing-radius-pill);
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  // ===== ACTIVITY CONTENT =====
  
  .wellbeing-activity-meta {
    display: flex;
    align-items: center;
    gap: var(--wellbeing-space-2);
    margin-top: var(--wellbeing-space-1);
    
    .wellbeing-duration {
      font-size: var(--wellbeing-font-xs);
      line-height: 16px;
      color: var(--wellbeing-text-tertiary);
    }
    
    .wellbeing-instructor {
      font-size: var(--wellbeing-font-xs);
      line-height: 16px;
      color: var(--wellbeing-text-secondary);
    }
    
    .wellbeing-separator {
      color: #D1D5DB;
      font-size: var(--wellbeing-font-base);
      line-height: 24px;
    }
  }
  
  // ===== RESPONSIVE DESIGN =====
  
  @media (min-width: 768px) {
    .wellbeing-container {
      max-width: 768px;
      padding: 0 var(--wellbeing-space-8);
    }
    
    .wellbeing-grid-2 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    .wellbeing-mood-buttons {
      justify-content: center;
    }
  }
  
  @media (min-width: 1024px) {
    .wellbeing-container {
      max-width: 1024px;
      padding: 0 var(--wellbeing-space-10);
    }
    
    .wellbeing-grid-2 {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  // ===== ANIMATION CLASSES =====
  
  .wellbeing-animate-fade-in {
    animation: wellbeingFadeIn 0.3s ease-out;
  }
  
  .wellbeing-animate-slide-up {
    animation: wellbeingSlideUp 0.5s ease-out;
  }
  
  .wellbeing-animate-mood-select {
    animation: wellbeingMoodSelect 0.2s ease-out;
  }
  
  @keyframes wellbeingFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes wellbeingSlideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes wellbeingMoodSelect {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
  
  // ===== IONIC COMPONENT OVERRIDES =====
  
  // Override Ionic components to match wellbeing theme
  ion-toolbar {
    --background: var(--wellbeing-bg-primary);
    --color: var(--wellbeing-text-primary);
    --border-color: transparent;
  }
  
  ion-content {
    --background: var(--wellbeing-bg-primary);
    --color: var(--wellbeing-text-primary);
  }
  
  ion-header {
    &::after {
      display: none; // Remove default Ionic header border
    }
  }
  
  ion-tab-bar {
    --background: var(--wellbeing-bg-secondary);
    --border: 1px solid var(--wellbeing-border-light);
  }
  
  ion-tab-button {
    --color: var(--wellbeing-text-quaternary);
    --color-selected: var(--wellbeing-accent);
  }
  
  // ===== ACCESSIBILITY =====
  
  .wellbeing-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  // Focus styles for better accessibility
  .wellbeing-focusable {
    &:focus-visible {
      outline: 2px solid var(--wellbeing-accent);
      outline-offset: 2px;
      border-radius: var(--wellbeing-radius-sm);
    }
  }
  
  // High contrast mode support
  @media (prefers-contrast: high) {
    .wellbeing {
      --wellbeing-text-secondary: #3E342A;
      --wellbeing-text-tertiary: #2D2520;
      --wellbeing-border-light: #C19660;
    }
  }
  
  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    .wellbeing * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
} 