server {
    listen 80;
    server_name localhost;

    # Set maximum upload size to 500MB
    client_max_body_size 500M;
    
    # Set timeout for large uploads
    client_body_timeout 300s;
    client_header_timeout 300s;
    proxy_read_timeout 300s;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;

    # Serve static files (videos, thumbnails, etc.)
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ =404;
        
        # Enable CORS for all files
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        
        # Set appropriate MIME types for video files
        location ~* \.(m3u8)$ {
            add_header Content-Type application/vnd.apple.mpegurl;
            add_header Access-Control-Allow-Origin *;
            expires 1h;
        }
        
        location ~* \.(ts)$ {
            add_header Content-Type video/mp2t;
            add_header Access-Control-Allow-Origin *;
            expires 24h;
        }
        
        location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv)$ {
            add_header Access-Control-Allow-Origin *;
            expires 24h;
        }
        
        location ~* \.(jpg|jpeg|png|gif)$ {
            add_header Access-Control-Allow-Origin *;
            expires 1h;
        }
    }

    # Gzip compression for text files
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/vnd.apple.mpegurl;
} 