package com.nutrition.hibernatedemo.repository;

import com.nutrition.hibernatedemo.HibernateDemoTestBase;
import com.nutrition.hibernatedemo.entity.Person;
import com.nutrition.hibernatedemo.model.Description;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PersonRepository
 */
@ActiveProfiles("test")
@Transactional
@DataJpaTest
class PersonRepositoryTest{

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private PersonRepository personRepository;

    private Person testPerson1;
    private Person testPerson2;
    private Person testPerson3;

    @BeforeEach
    void setUp() {
        // Create test data
        testPerson1 = new Person();
        testPerson1.setName("John Doe");
        testPerson1.setEmail("<EMAIL>");
        testPerson1.setDescription(new Description("Programming", "Blue", 28, "Software developer"));
        testPerson1.setSkills(Arrays.asList("Java", "Spring", "Angular"));
        testPerson1.setLanguages(Arrays.asList("English", "Spanish"));

        testPerson2 = new Person();
        testPerson2.setName("Jane Smith");
        testPerson2.setEmail("<EMAIL>");
        testPerson2.setDescription(new Description("Data Analysis", "Green", 32, "Data scientist"));
        testPerson2.setSkills(Arrays.asList("Python", "Machine Learning", "SQL"));
        testPerson2.setLanguages(Arrays.asList("English", "German"));

        testPerson3 = new Person();
        testPerson3.setName("Mike Johnson");
        testPerson3.setEmail("<EMAIL>");
        testPerson3.setDescription(new Description("Programming", "Red", 26, "Frontend developer"));
        testPerson3.setSkills(Arrays.asList("JavaScript", "React", "Angular"));
        testPerson3.setLanguages(Arrays.asList("English", "French"));

        // Save test data
        entityManager.persistAndFlush(testPerson1);
        entityManager.persistAndFlush(testPerson2);
        entityManager.persistAndFlush(testPerson3);
    }

    @Test
    void testFindByEmail() {
        Person found = personRepository.findByEmail("<EMAIL>");
        assertNotNull(found);
        assertEquals("John Doe", found.getName());
    }

    @Test
    void testFindByHobby() {
        List<Person> programmers = personRepository.findByHobby("Programming");
        assertEquals(2, programmers.size());
        assertTrue(programmers.stream().anyMatch(p -> p.getName().equals("John Doe")));
        assertTrue(programmers.stream().anyMatch(p -> p.getName().equals("Mike Johnson")));
    }

    @Test
    void testFindByHobbyIn() {
        List<String> hobbies = Arrays.asList("Programming", "Data Analysis");
        List<Person> found = personRepository.findByHobbyIn(hobbies);
        assertEquals(3, found.size());
    }

    @Test
    void testFindByAgeRange() {
        List<Person> found = personRepository.findByAgeRange(25, 30);
        assertEquals(2, found.size());
        assertTrue(found.stream().anyMatch(p -> p.getName().equals("John Doe")));
        assertTrue(found.stream().anyMatch(p -> p.getName().equals("Mike Johnson")));
    }

    @Test
    void testFindByFavoriteColor() {
        List<Person> blueLovers = personRepository.findByFavoriteColor("Blue");
        assertEquals(1, blueLovers.size());
        assertEquals("John Doe", blueLovers.get(0).getName());
    }

    @Test
    void testFindBySkill() {
        List<Person> angularDevelopers = personRepository.findBySkill("Angular");
        assertEquals(2, angularDevelopers.size());
        assertTrue(angularDevelopers.stream().anyMatch(p -> p.getName().equals("John Doe")));
        assertTrue(angularDevelopers.stream().anyMatch(p -> p.getName().equals("Mike Johnson")));
    }

    @Test
    void testFindByAnySkill() {
        String[] skills = {"Python", "React"};
        List<Person> found = personRepository.findByAnySkill(skills);
        assertEquals(2, found.size());
        assertTrue(found.stream().anyMatch(p -> p.getName().equals("Jane Smith")));
        assertTrue(found.stream().anyMatch(p -> p.getName().equals("Mike Johnson")));
    }

    @Test
    void testFindByLanguage() {
        List<Person> englishSpeakers = personRepository.findByLanguage("English");
        assertEquals(3, englishSpeakers.size()); // All test persons speak English
    }

    @Test
    void testFindByAllLanguages() {
        String[] languages = {"English", "Spanish"};
        List<Person> found = personRepository.findByAllLanguages(languages);
        assertEquals(1, found.size());
        assertEquals("John Doe", found.get(0).getName());
    }

    @Test
    void testFindByBioContaining() {
        List<Person> developers = personRepository.findByBioContaining("developer");
        assertEquals(2, developers.size());
        assertTrue(developers.stream().anyMatch(p -> p.getName().equals("John Doe")));
        assertTrue(developers.stream().anyMatch(p -> p.getName().equals("Mike Johnson")));
    }

    @Test
    void testFindByHobbyAndSkill() {
        List<Person> programmersWithAngular = personRepository.findByHobbyAndSkill("Programming", "Angular");
        assertEquals(2, programmersWithAngular.size());
    }

    @Test
    void testFindByMinimumSkillCount() {
        List<Person> skilledPersons = personRepository.findByMinimumSkillCount(3);
        assertEquals(3, skilledPersons.size()); // All test persons have 3 skills
    }

    @Test
    void testFindByJsonContains() {
        String jsonData = "{\"hobby\": \"Programming\"}";
        List<Person> found = personRepository.findByJsonContains(jsonData);
        assertEquals(2, found.size());
    }

    @Test
    void testGetAllUniqueSkills() {
        List<String> uniqueSkills = personRepository.getAllUniqueSkills();
        assertTrue(uniqueSkills.contains("Java"));
        assertTrue(uniqueSkills.contains("Python"));
        assertTrue(uniqueSkills.contains("React"));
        assertTrue(uniqueSkills.contains("Angular"));
    }

    @Test
    void testGetHobbyStatistics() {
        List<Object[]> stats = personRepository.getHobbyStatistics();
        assertFalse(stats.isEmpty());

        // Find Programming hobby stats
        Object[] programmingStats = stats.stream()
            .filter(stat -> "Programming".equals(stat[0]))
            .findFirst()
            .orElse(null);

        assertNotNull(programmingStats);
        assertEquals(2L, programmingStats[1]); // 2 people have Programming as hobby
    }

    @Test
    void testFindPeopleWithSameSkillsAs() {
        // First persist a person with Angular skill to compare against
        Long johnId = testPerson1.getId();
        assertNotNull(johnId);

        List<Person> similarPeople = personRepository.findPeopleWithSameSkillsAs(johnId);
        // Should be empty since no one has exactly the same skill set
        assertTrue(similarPeople.isEmpty());
    }
}
