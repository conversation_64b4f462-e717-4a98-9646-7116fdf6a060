import { Routes } from '@angular/router';

export const VIDEO_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./video-list/video-list.page').then(m => m.VideoListPage)
  },
  {
    path: 'upload',
    loadComponent: () => import('./video-upload/video-upload.page').then(m => m.VideoUploadPage)
  },
  {
    path: 'player/:id',
    loadComponent: () => import('./video-player/video-player.page').then(m => m.VideoPlayerPage)
  }
];
