import { httpResource } from '@angular/common/http';
import { Injectable, computed, signal } from '@angular/core';

export interface PlaygroundItem {
  id: number;
  name: string;
  description: string;
  category: string;
  priority: string;
  createdDate: string;
  active: boolean;
}

export interface PlaygroundResponse {
  items: PlaygroundItem[];
  totalElements: number;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
}

export interface PlaygroundFilters {
  page: number;
  size: number;
  filter: string;
  sortBy: 'name' | 'category' | 'priority' | 'date';
  sortDirection: 'asc' | 'desc';
}

@Injectable({
  providedIn: 'root',
})
export class PlaygroundService {
  private readonly baseUrl = '/api/playground';
  
  // Reactive state for filters
  filters = signal<PlaygroundFilters>({
    page: 0,
    size: 20,
    filter: '',
    sortBy: 'name',
    sortDirection: 'asc'
  });

  // httpResource that reacts to filter changes
  itemsResource = httpResource<PlaygroundResponse>(() => {
    const currentFilters = this.filters();
    const params = new URLSearchParams({
      page: currentFilters.page.toString(),
      size: currentFilters.size.toString(),
      filter: currentFilters.filter,
      sortBy: currentFilters.sortBy,
      sortDirection: currentFilters.sortDirection
    });
    
    return `${this.baseUrl}/items?${params.toString()}`;
  });

  // Computed values for easier access
  items = computed(() => this.itemsResource.value()?.items || []);
  totalElements = computed(() => this.itemsResource.value()?.totalElements || 0);
  hasNext = computed(() => this.itemsResource.value()?.hasNext || false);
  isLoading = computed(() => this.itemsResource.isLoading());
  error = computed(() => this.itemsResource.error());

  // Filter management methods
  updateFilter(filter: string) {
    this.filters.update(current => ({
      ...current,
      filter,
      page: 0 // Reset to first page when filtering
    }));
  }

  updateSort(sortBy: PlaygroundFilters['sortBy']) {
    this.filters.update(current => ({
      ...current,
      sortBy,
      sortDirection: current.sortBy === sortBy && current.sortDirection === 'asc' ? 'desc' : 'asc',
      page: 0 // Reset to first page when sorting
    }));
  }

  loadMore() {
    if (this.hasNext()) {
      this.filters.update(current => ({
        ...current,
        page: current.page + 1
      }));
    }
  }

  refresh() {
    this.itemsResource.reload();
  }

  reset() {
    this.filters.set({
      page: 0,
      size: 50,
      filter: '',
      sortBy: 'name',
      sortDirection: 'asc'
    });
  }
} 