package com.nutrition.hibernatedemo.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrition.hibernatedemo.entity.Person;
import com.nutrition.hibernatedemo.model.Description;
import com.nutrition.hibernatedemo.repository.PersonRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for PersonController
 */
@WebMvcTest(PersonController.class)
class PersonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PersonRepository personRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Person testPerson;
    private List<Person> testPersons;

    @BeforeEach
    void setUp() {
        testPerson = new Person();
        testPerson.setId(1L);
        testPerson.setName("John Doe");
        testPerson.setEmail("<EMAIL>");
        testPerson.setDescription(new Description("Programming", "Blue", 28, "Software developer"));
        testPerson.setSkills(Arrays.asList("Java", "Spring", "Angular"));
        testPerson.setLanguages(Arrays.asList("English", "Spanish"));

        Person testPerson2 = new Person();
        testPerson2.setId(2L);
        testPerson2.setName("Jane Smith");
        testPerson2.setEmail("<EMAIL>");
        testPerson2.setDescription(new Description("Data Analysis", "Green", 32, "Data scientist"));
        testPerson2.setSkills(Arrays.asList("Python", "Machine Learning"));
        testPerson2.setLanguages(Arrays.asList("English", "German"));

        testPersons = Arrays.asList(testPerson, testPerson2);
    }

    @Test
    void getAllPersons_ShouldReturnAllPersons() throws Exception {
        when(personRepository.findAll()).thenReturn(testPersons);

        mockMvc.perform(get("/api/hibernate-demo/persons"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].name").value("John Doe"))
                .andExpect(jsonPath("$[1].name").value("Jane Smith"));
    }

    @Test
    void getPersonById_ShouldReturnPerson_WhenPersonExists() throws Exception {
        when(personRepository.findById(1L)).thenReturn(Optional.of(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("John Doe"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.description.hobby").value("Programming"));
    }

    @Test
    void getPersonById_ShouldReturnNotFound_WhenPersonDoesNotExist() throws Exception {
        when(personRepository.findById(999L)).thenReturn(Optional.empty());

        mockMvc.perform(get("/api/hibernate-demo/persons/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    void createPerson_ShouldReturnCreatedPerson() throws Exception {
        when(personRepository.save(any(Person.class))).thenReturn(testPerson);

        mockMvc.perform(post("/api/hibernate-demo/persons")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPerson)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("John Doe"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    @Test
    void updatePerson_ShouldReturnUpdatedPerson_WhenPersonExists() throws Exception {
        when(personRepository.findById(1L)).thenReturn(Optional.of(testPerson));
        when(personRepository.save(any(Person.class))).thenReturn(testPerson);

        mockMvc.perform(put("/api/hibernate-demo/persons/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPerson)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("John Doe"));
    }

    @Test
    void updatePerson_ShouldReturnNotFound_WhenPersonDoesNotExist() throws Exception {
        when(personRepository.findById(999L)).thenReturn(Optional.empty());

        mockMvc.perform(put("/api/hibernate-demo/persons/999")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPerson)))
                .andExpect(status().isNotFound());
    }

    @Test
    void deletePerson_ShouldReturnNoContent_WhenPersonExists() throws Exception {
        when(personRepository.existsById(1L)).thenReturn(true);

        mockMvc.perform(delete("/api/hibernate-demo/persons/1"))
                .andExpect(status().isNoContent());
    }

    @Test
    void deletePerson_ShouldReturnNotFound_WhenPersonDoesNotExist() throws Exception {
        when(personRepository.existsById(999L)).thenReturn(false);

        mockMvc.perform(delete("/api/hibernate-demo/persons/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getPersonsByHobby_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByHobby("Programming")).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-hobby/Programming"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsByHobbies_ShouldReturnFilteredPersons() throws Exception {
        List<String> hobbies = Arrays.asList("Programming", "Data Analysis");
        when(personRepository.findByHobbyIn(hobbies)).thenReturn(testPersons);

        mockMvc.perform(post("/api/hibernate-demo/persons/by-hobbies")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(hobbies)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2));
    }

    @Test
    void getPersonsByAgeRange_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByAgeRange(25, 30)).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-age-range")
                .param("minAge", "25")
                .param("maxAge", "30"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsByFavoriteColor_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByFavoriteColor("Blue")).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-favorite-color/Blue"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsBySkill_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findBySkill("Java")).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-skill/Java"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsByAnySkill_ShouldReturnFilteredPersons() throws Exception {
        String[] skills = {"Java", "Python"};
        when(personRepository.findByAnySkill(skills)).thenReturn(testPersons);

        mockMvc.perform(post("/api/hibernate-demo/persons/by-any-skill")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(skills)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2));
    }

    @Test
    void getPersonsByLanguage_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByLanguage("English")).thenReturn(testPersons);

        mockMvc.perform(get("/api/hibernate-demo/persons/by-language/English"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2));
    }

    @Test
    void getPersonsByAllLanguages_ShouldReturnFilteredPersons() throws Exception {
        String[] languages = {"English", "Spanish"};
        when(personRepository.findByAllLanguages(languages)).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(post("/api/hibernate-demo/persons/by-all-languages")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(languages)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsByBioKeyword_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByBioContaining("developer")).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-bio-keyword/developer"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonsByMinimumSkills_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByMinimumSkillCount(2)).thenReturn(testPersons);

        mockMvc.perform(get("/api/hibernate-demo/persons/by-minimum-skills/2"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2));
    }

    @Test
    void getPersonsByHobbyAndSkill_ShouldReturnFilteredPersons() throws Exception {
        when(personRepository.findByHobbyAndSkill("Programming", "Java")).thenReturn(Arrays.asList(testPerson));

        mockMvc.perform(get("/api/hibernate-demo/persons/by-hobby-and-skill")
                .param("hobby", "Programming")
                .param("skill", "Java"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].name").value("John Doe"));
    }

    @Test
    void getPersonByEmail_ShouldReturnPerson_WhenPersonExists() throws Exception {
        when(personRepository.findByEmail("<EMAIL>")).thenReturn(testPerson);

        mockMvc.perform(get("/api/hibernate-demo/persons/by-email/<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("John Doe"));
    }

    @Test
    void getPersonByEmail_ShouldReturnNotFound_WhenPersonDoesNotExist() throws Exception {
        when(personRepository.findByEmail("<EMAIL>")).thenReturn(null);

        mockMvc.perform(get("/api/hibernate-demo/persons/by-email/<EMAIL>"))
                .andExpect(status().isNotFound());
    }

    @Test
    void getPersonCount_ShouldReturnCount() throws Exception {
        when(personRepository.count()).thenReturn(2L);

        mockMvc.perform(get("/api/hibernate-demo/persons/count"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().string("2"));
    }
} 